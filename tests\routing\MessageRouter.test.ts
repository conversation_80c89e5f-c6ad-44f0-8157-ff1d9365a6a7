/**
 * MessageRouter 单元测试
 * 测试消息路由器的核心功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MessageRouter } from '../../src/routing/MessageRouter';
import { EventBus } from '../../src/core/EventBus';
import { testUtils } from '../setup';

describe('MessageRouter', () => {
  let messageRouter: MessageRouter;
  let mockEventBus: ReturnType<typeof testUtils.createMockEventBus>;

  beforeEach(() => {
    // 创建模拟的EventBus
    mockEventBus = testUtils.createMockEventBus();

    // 创建MessageRouter实例
    messageRouter = new MessageRouter(
      {
        defaultScenario: 'custom-component',
        debug: true,
      },
      mockEventBus as unknown as EventBus
    );
  });

  describe('初始化', () => {
    it('应该正确初始化默认状态', () => {
      const routingState = messageRouter.getRoutingState();

      expect(routingState.currentScenario).toBe('custom-component');
      expect(routingState.isActive).toBe(true);
      expect(routingState.sessionId).toBeDefined();
      expect(routingState.sessionId).toMatch(/^test-uuid-/); // 测试环境UUID格式
    });

    it('应该正确初始化路由上下文', () => {
      const routingContext = messageRouter.getRoutingContext();

      expect(routingContext.hasCustomComponent).toBe(false);
      expect(routingContext.isGreetingPageVisible).toBe(false);
    });

    it('应该绑定所有必要的事件监听器', () => {
      // 验证关键事件已被监听
      expect(mockEventBus.on).toHaveBeenCalledWith('hkstt:asr-offline-result', expect.any(Function));
      expect(mockEventBus.on).toHaveBeenCalledWith('ai:send-chat-request', expect.any(Function));
      expect(mockEventBus.on).toHaveBeenCalledWith('router:switch-scenario', expect.any(Function));
      expect(mockEventBus.on).toHaveBeenCalledWith('component:custom-component-ready', expect.any(Function));
      expect(mockEventBus.on).toHaveBeenCalledWith('component:greeting-page-visible', expect.any(Function));

      // 注意：不再监听AI响应事件，因为流式响应由组件直接处理
    });
  });

  describe('场景切换', () => {
    it('应该能够切换到打招呼页面场景', () => {
      const testSessionId = 'test-session-uuid';

      messageRouter.switchScenario('greeting-page', testSessionId);

      const routingState = messageRouter.getRoutingState();
      expect(routingState.currentScenario).toBe('greeting-page');
      expect(routingState.sessionId).toBe(testSessionId);

      // 验证场景切换事件被发送
      expect(mockEventBus.emit).toHaveBeenCalledWith('router:scenario-changed', {
        from: 'custom-component',
        to: 'greeting-page',
        sessionId: testSessionId,
        timestamp: expect.any(Number),
      });
    });

    it('应该能够切换到自定义组件场景', () => {
      // 先切换到打招呼页面
      messageRouter.switchScenario('greeting-page');

      const testSessionId = 'test-custom-session-uuid';
      messageRouter.switchScenario('custom-component', testSessionId);

      const routingState = messageRouter.getRoutingState();
      expect(routingState.currentScenario).toBe('custom-component');
      expect(routingState.sessionId).toBe(testSessionId);
    });

    it('应该在没有提供sessionId时自动生成UUID', () => {
      messageRouter.switchScenario('greeting-page');

      const routingState = messageRouter.getRoutingState();
      expect(routingState.sessionId).toMatch(/^test-uuid-/); // 测试环境UUID格式
    });
  });

  describe('ASR结果处理', () => {
    it('应该正确处理ASR识别结果', () => {
      const asrData = {
        sid: 'test-sid',
        text: '我想查询余额',
      };

      // 模拟ASR结果事件
      mockEventBus.emit('hkstt:asr-offline-result', asrData);

      // 验证AI请求被发送
      expect(mockEventBus.emit).toHaveBeenCalledWith('ai:send-chat-request-internal', {
        userInput: asrData.text,
        sessionId: expect.stringMatching(/^test-uuid-/), // 测试环境UUID格式
        requestId: expect.any(String),
      });

      // 验证用户输入事件被发送到目标组件
      expect(mockEventBus.emit).toHaveBeenCalledWith('custom-component:user-input', {
        userInput: asrData.text,
        sessionId: expect.stringMatching(/^test-uuid-/),
        requestId: expect.any(String),
      });
    });

    it('应该根据打招呼页面可见性路由到正确组件', () => {
      // 设置打招呼页面可见
      mockEventBus.emit('component:greeting-page-visible', { sessionId: 'greeting-session-uuid' });

      const asrData = {
        sid: 'test-sid',
        text: '你好',
      };

      // 清除之前的调用记录
      vi.clearAllMocks();

      // 模拟ASR结果事件
      mockEventBus.emit('hkstt:asr-offline-result', asrData);

      // 验证用户输入事件被发送到打招呼页面
      expect(mockEventBus.emit).toHaveBeenCalledWith('greeting-page:user-input', {
        userInput: asrData.text,
        sessionId: 'greeting-session-uuid',
        requestId: expect.any(String),
      });
    });
  });

  describe('AI请求拦截', () => {
    it('应该正确拦截和转发AI请求', () => {
      const chatRequest = {
        userInput: '我想办储蓄卡',
        sessionId: 'test-session-uuid',
        requestId: 'test-request-id',
      };

      // 模拟AI请求事件
      mockEventBus.emit('ai:send-chat-request', chatRequest);

      // 验证请求被转发到内部处理
      expect(mockEventBus.emit).toHaveBeenCalledWith('ai:send-chat-request-internal', {
        userInput: chatRequest.userInput,
        sessionId: chatRequest.sessionId,
        requestId: chatRequest.requestId,
      });
    });

    it('应该处理空sessionId的情况', () => {
      const chatRequest = {
        userInput: '测试消息',
        sessionId: '',
        requestId: 'test-request-id',
      };

      // 模拟AI请求事件
      mockEventBus.emit('ai:send-chat-request', chatRequest);

      // 验证使用当前路由状态的sessionId
      const routingState = messageRouter.getRoutingState();
      expect(mockEventBus.emit).toHaveBeenCalledWith('ai:send-chat-request-internal', {
        userInput: chatRequest.userInput,
        sessionId: routingState.sessionId,
        requestId: chatRequest.requestId,
      });
    });
  });

  // 注意：AI响应处理测试已移除，因为MessageRouter不再处理AI响应
  // 流式响应由组件直接通过 ai:stream-message-chunk 事件处理

  describe('组件状态管理', () => {
    it('应该正确更新自定义组件就绪状态', () => {
      mockEventBus.emit('component:custom-component-ready', {});

      const routingContext = messageRouter.getRoutingContext();
      expect(routingContext.hasCustomComponent).toBe(true);
    });

    it('应该正确更新打招呼页面可见状态', () => {
      const sessionId = 'greeting-session-uuid';

      mockEventBus.emit('component:greeting-page-visible', { sessionId });

      const routingContext = messageRouter.getRoutingContext();
      expect(routingContext.isGreetingPageVisible).toBe(true);
    });

    it('应该正确处理打招呼页面隐藏事件', () => {
      // 先设置为可见
      mockEventBus.emit('component:greeting-page-visible', { sessionId: 'test-session' });

      // 然后隐藏
      mockEventBus.emit('component:greeting-page-hidden', {});

      const routingContext = messageRouter.getRoutingContext();
      expect(routingContext.isGreetingPageVisible).toBe(false);
    });
  });

  describe('打招呼页面返回处理', () => {
    it('应该正确处理打招呼页面返回事件', async () => {
      // 设置初始状态：打招呼页面可见
      mockEventBus.emit('component:greeting-page-visible', { sessionId: 'greeting-session' });

      // 清除之前的调用记录
      vi.clearAllMocks();

      // 模拟打招呼页面返回事件
      mockEventBus.emit('greeting-page:back', { sessionId: 'greeting-session' });

      // 等待异步操作完成
      await testUtils.wait(250);

      // 验证场景切换到自定义组件
      const routingState = messageRouter.getRoutingState();
      expect(routingState.currentScenario).toBe('custom-component');

      // 验证打招呼页面状态更新为不可见
      const routingContext = messageRouter.getRoutingContext();
      expect(routingContext.isGreetingPageVisible).toBe(false);

      // 验证相关事件被发送
      expect(mockEventBus.emit).toHaveBeenCalledWith('sdk:cleanup-on-page-switch');
      expect(mockEventBus.emit).toHaveBeenCalledWith('custom-component:session-sync', {
        sessionId: expect.stringMatching(/^test-uuid-/),
        reason: 'greeting-page-return',
      });
      expect(mockEventBus.emit).toHaveBeenCalledWith('sdk:remove-greeting-page');
    });
  });

  describe('调试信息', () => {
    it('应该提供完整的调试信息', () => {
      const debugInfo = messageRouter.getDebugInfo();

      expect(debugInfo).toHaveProperty('routingState');
      expect(debugInfo).toHaveProperty('routingContext');
      expect(debugInfo).toHaveProperty('config');
      expect(debugInfo).toHaveProperty('timestamp');

      expect(debugInfo.routingState).toEqual(messageRouter.getRoutingState());
      expect(debugInfo.routingContext).toEqual(messageRouter.getRoutingContext());
    });
  });

  describe('销毁', () => {
    it('应该能够正确销毁路由器', () => {
      expect(() => {
        messageRouter.destroy();
      }).not.toThrow();
    });
  });
});
