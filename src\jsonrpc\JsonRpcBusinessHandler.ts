/**
 * JSON-RPC业务逻辑处理器
 * 负责处理client:send-request事件并路由到相应的业务逻辑
 * 简化架构，直接调用AIClient和ServiceCoordinator的方法
 *
 * 注意：此文件中的 any 类型使用是合理的，因为 JSON-RPC 标准允许任意 JSON 值作为参数和结果
 */

import { EventBus } from '../core/EventBus';
import { PageStateManager, PageState } from '../core/PageStateManager';
import { MessageRouter } from '../routing/MessageRouter';
import { AIClient } from '../services/AIClient';
import { ServiceCoordinator } from '../services/ServiceCoordinator';
import {
  SpeakParams,
  UpdateBackgroundInfoParams,
  AddMessagesParams,
  PushBizDataParams,
  SpeakModeParams,
  isSpeakParams,
  isUpdateBackgroundInfoParams,
  isAddMessagesParams,
  isPushBizDataParams,
  isSpeakModeParams,
} from '../types/jsonrpc';
import { JsonRpcRequest } from '../types/jsonrpc';
import { generateUUID } from '../utils/helpers';
import { Logger } from '../utils/Logger';

/**
 * JSON-RPC业务逻辑处理器
 */
export class JsonRpcBusinessHandler {
  private eventBus: EventBus;
  private logger: Logger;
  private aiClient: AIClient;
  private serviceCoordinator: ServiceCoordinator;
  private messageRouter: MessageRouter;
  private pageStateManager: PageStateManager;

  constructor(
    eventBus: EventBus,
    aiClient: AIClient,
    serviceCoordinator: ServiceCoordinator,
    messageRouter: MessageRouter,
    pageStateManager: PageStateManager
  ) {
    this.eventBus = eventBus;
    this.aiClient = aiClient;
    this.serviceCoordinator = serviceCoordinator;
    this.messageRouter = messageRouter;
    this.pageStateManager = pageStateManager;
    this.logger = Logger.getInstance({ prefix: 'JsonRpcBusinessHandler' });

    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听client:send-request事件，处理实际的业务逻辑
    this.eventBus.on('client:send-request', (data: unknown) => {
      this.handleBusinessRequest(data as JsonRpcRequest);
    });
  }

  /**
   * 处理业务请求
   */
  private async handleBusinessRequest(request: JsonRpcRequest): Promise<void> {
    const { method, params, id } = request;

    this.logger.info('🎯 处理JSON-RPC业务请求', {
      method,
      id,
      hasParams: params !== undefined,
    });

    try {
      let result: unknown;

      // 根据方法名路由到相应的业务逻辑
      switch (method) {
        case 'speak':
          if (!isSpeakParams(params)) {
            throw new Error('Invalid speak parameters');
          }
          result = await this.handleSpeak(params);
          break;

        case 'updateBackgroundInfo':
          if (!isUpdateBackgroundInfoParams(params)) {
            throw new Error('Invalid updateBackgroundInfo parameters');
          }
          result = await this.handleUpdateBackgroundInfo(params);
          break;

        case 'addMessages':
          if (!isAddMessagesParams(params)) {
            throw new Error('Invalid addMessages parameters');
          }
          result = await this.handleAddMessages(params);
          break;

        case 'pushBizData':
          if (!isPushBizDataParams(params)) {
            throw new Error('Invalid pushBizData parameters');
          }
          result = await this.handlePushBizData(params);
          break;

        case 'speakMode':
          if (!isSpeakModeParams(params)) {
            throw new Error('Invalid speakMode parameters');
          }
          result = await this.handleSpeakMode(params);
          break;

        case 'clientUI':
          result = await this.handleClientUI(params);
          break;

        default:
          throw new Error(`不支持的方法: ${method}`);
      }

      // 发送成功响应
      this.eventBus.emit('jsonrpc:response', {
        jsonrpc: '2.0',
        result,
        id,
      });

      this.logger.info('✅ JSON-RPC业务请求处理成功', {
        method,
        id,
        result: typeof result === 'object' && result !== null ? Object.keys(result) : result,
      });
    } catch (error) {
      // 发送错误响应
      this.eventBus.emit('jsonrpc:error-response', {
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: `业务逻辑错误: ${(error as Error).message}`,
          data: { method, params },
        },
        id,
      });

      this.logger.error('❌ JSON-RPC业务请求处理失败', {
        method,
        id,
        error: (error as Error).message,
      });
    }
  }

  /**
   * 处理speak请求 - 通过ServiceCoordinator
   */
  private async handleSpeak(params: SpeakParams): Promise<{
    success: boolean;
    message?: string;
    text?: string;
    delay?: number;
    display?: boolean;
    sessionId?: string;
    targetComponent?: string;
    language?: string;
  }> {
    const { text, delay = 0, display = true } = params;

    this.logger.info('🗣️ 处理speak请求', { text: text?.substring(0, 50), delay, display });

    try {
      // 获取活跃组件信息（包含正确的会话ID和语言设置）
      const activeComponentInfo = this.getActiveComponentInfo();

      // 确保ServiceCoordinator的语言状态与活跃组件一致
      this.eventBus.emit('tts:language-change', { language: activeComponentInfo.language });

      // 验证语言状态同步
      const currentLanguage = this.serviceCoordinator.getCurrentLanguage();
      this.logger.info('🗣️ 当前TTS语言状态', {
        language: currentLanguage,
        activeComponent: activeComponentInfo.target,
        activeSessionId: activeComponentInfo.sessionId,
        instructText: currentLanguage === 'chuanyu' ? '用四川话说这句话' : '用自然清晰的普通话说话',
      });

      // 第一步：调用TTS服务进行语音合成和播放
      // 使用活跃组件的正确会话ID，确保消息能正确路由
      const mockAIResponse = {
        message: text,
        sessionId: activeComponentInfo.sessionId, // 使用活跃组件的会话ID
        requestId: generateUUID(), // 使用纯UUID
      };

      // 如果有延时，先等待
      if (delay > 0) {
        this.logger.info(`⏰ 延时 ${delay}ms 后开始TTS播放`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // 发送到ServiceCoordinator处理TTS
      this.eventBus.emit('ai:chat-final-response', mockAIResponse);

      // 检查ServiceCoordinator的服务状态
      const serviceStatus = this.serviceCoordinator.getServiceStatus();
      this.logger.info('📊 当前服务状态', { serviceStatus });

      // 第二步：如果display参数为true，则调用aiClient.addMessages()方法将消息添加到对话历史中
      if (display) {
        this.logger.info('📝 将消息添加到对话历史中');

        try {
          const addMessagesResult = await this.aiClient.sendJsonRpcRequest('addMessages', {
            sessionId: activeComponentInfo.sessionId, // 使用活跃组件的会话ID
            messages: [
              {
                role: 'assistant',
                content: text,
              },
            ],
          });

          this.logger.info('✅ 消息已添加到对话历史', { result: addMessagesResult });
        } catch (error) {
          this.logger.error('❌ 添加消息到对话历史失败', { error: (error as Error).message });
          // 不抛出错误，因为TTS播放已经开始，这只是附加功能
        }
      }

      return {
        success: true,
        message: 'TTS播放已开始',
        text,
        delay,
        display,
        sessionId: activeComponentInfo.sessionId, // 返回实际使用的会话ID
        targetComponent: activeComponentInfo.target,
        language: activeComponentInfo.language,
      };
    } catch (error) {
      this.logger.error('❌ speak请求处理失败', { error: (error as Error).message });
      throw error;
    }
  }

  /**
   * 处理updateBackgroundInfo请求 - 通过AIClient
   */
  private async handleUpdateBackgroundInfo(
    params: UpdateBackgroundInfoParams
  ): Promise<Record<string, unknown>> {
    // 添加调试日志
    this.logger.info('🔍 handleUpdateBackgroundInfo 开始处理', {
      params,
      hasParams: !!params,
      paramsType: typeof params,
      hasStatus: params && typeof params === 'object' && 'status' in params,
    });

    // 检查是否包含页面状态切换信息
    if (params && typeof params === 'object' && 'status' in params) {
      const status = params.status as string;

      this.logger.info('🔍 检测到status参数', {
        status,
        statusType: typeof status,
        isValidPageState: this.isValidPageState(status),
      });

      // 检查是否为有效的页面状态
      if (this.isValidPageState(status)) {
        const pageState = status as PageState;

        this.logger.info('🔄 检测到页面状态切换请求', {
          currentState: this.pageStateManager.getCurrentState(),
          requestedState: pageState,
          params,
        });

        // 切换页面状态
        this.pageStateManager.setState(pageState);
      } else {
        this.logger.warn('⚠️ 无效的页面状态', {
          status,
          validStates: ['WaitCardPage', 'AppSelectAuto', 'CreditSelectAuto'],
        });
      }
    } else {
      this.logger.info('ℹ️ 未检测到status参数', {
        params,
        hasParams: !!params,
        paramsKeys: params && typeof params === 'object' ? Object.keys(params) : [],
      });
    }

    // 获取活跃组件信息，使用SDK内部管理的会话ID
    const activeComponentInfo = this.getActiveComponentInfo();

    // 移除用户传入的sessionId，使用活跃组件的会话ID
    const finalParams = {
      ...params,
      sessionId: activeComponentInfo.sessionId, // 覆盖用户传入的sessionId
    };

    this.logger.info('📝 处理updateBackgroundInfo请求', {
      originalParams: params,
      finalParams,
      targetComponent: activeComponentInfo.target,
      note: '会话ID已替换为活跃组件的会话ID',
    });

    // 调用AIClient的通用JSON-RPC方法
    const result = await this.aiClient.sendJsonRpcRequest('updateBackgroundInfo', finalParams);

    // 直接返回AI服务器的响应，不添加额外字段
    return typeof result === 'object' && result !== null ? (result as Record<string, unknown>) : {};
  }

  /**
   * 检查是否为有效的页面状态
   */
  private isValidPageState(status: string): boolean {
    return ['WaitCardPage', 'AppSelectAuto', 'CreditSelectAuto'].includes(status);
  }

  /**
   * 处理addMessages请求 - 通过AIClient
   */
  private async handleAddMessages(params: AddMessagesParams): Promise<Record<string, unknown>> {
    // 获取活跃组件信息，使用SDK内部管理的会话ID
    const activeComponentInfo = this.getActiveComponentInfo();

    // 移除用户传入的sessionId，使用活跃组件的会话ID
    const finalParams = {
      ...params,
      sessionId: activeComponentInfo.sessionId, // 覆盖用户传入的sessionId
    };

    this.logger.info('📨 处理addMessages请求', {
      originalParams: params,
      finalParams,
      targetComponent: activeComponentInfo.target,
      note: '会话ID已替换为活跃组件的会话ID',
    });

    // 调用AIClient的通用JSON-RPC方法
    const result = await this.aiClient.sendJsonRpcRequest('addMessages', finalParams);

    // 直接返回AI服务器的响应，不添加额外字段
    return typeof result === 'object' && result !== null ? (result as Record<string, unknown>) : {};
  }

  /**
   * 处理pushBizData请求 - 通过AIClient
   */
  private async handlePushBizData(params: PushBizDataParams): Promise<Record<string, unknown>> {
    // 获取活跃组件信息，使用SDK内部管理的会话ID
    const activeComponentInfo = this.getActiveComponentInfo();

    // 移除用户传入的sessionId，使用活跃组件的会话ID
    const finalParams = {
      ...params,
      sessionId: activeComponentInfo.sessionId, // 覆盖用户传入的sessionId
    };

    this.logger.info('📊 处理pushBizData请求', {
      originalParams: params,
      finalParams,
      targetComponent: activeComponentInfo.target,
      note: '会话ID已替换为活跃组件的会话ID',
    });

    // 调用AIClient的通用JSON-RPC方法
    const result = await this.aiClient.sendJsonRpcRequest('pushBizData', finalParams);

    // 直接返回AI服务器的响应，不添加额外字段
    return typeof result === 'object' && result !== null ? (result as Record<string, unknown>) : {};
  }

  /**
   * 获取活跃组件信息
   */
  private getActiveComponentInfo(): {
    target: 'greeting-page' | 'custom-component';
    sessionId: string;
    language: 'mandarin' | 'chuanyu';
  } {
    const routingContext = this.messageRouter.getRoutingContext();
    const routingState = this.messageRouter.getRoutingState();

    // 确定活跃组件（优先使用MessageRouter的路由上下文）
    let target: 'greeting-page' | 'custom-component';
    if (routingContext.isGreetingPageVisible) {
      target = 'greeting-page';
    } else {
      target = 'custom-component';
    }

    // 验证DOM中组件的实际可见性
    const isDOMVisible = this.isComponentVisibleInDOM(target);
    if (!isDOMVisible) {
      // 如果目标组件在DOM中不可见，切换到另一个组件
      target = target === 'greeting-page' ? 'custom-component' : 'greeting-page';
      this.logger.warn('⚠️ 目标组件在DOM中不可见，切换到备用组件', {
        originalTarget: target === 'greeting-page' ? 'custom-component' : 'greeting-page',
        fallbackTarget: target,
      });
    }

    // 使用MessageRouter的内部会话ID管理
    // 如果当前场景与目标组件不匹配，让MessageRouter为目标组件创建或获取会话ID
    const sessionId = routingState.sessionId;
    if (routingState.currentScenario !== target) {
      // 通过MessageRouter的内部机制获取或创建目标组件的会话ID
      // 这里我们信任MessageRouter的路由逻辑，使用当前的sessionId
      this.logger.info('🔄 当前场景与目标组件不匹配，使用MessageRouter的会话ID', {
        currentScenario: routingState.currentScenario,
        targetComponent: target,
        sessionId,
      });
    }

    // 获取当前语言设置
    const language = this.getCurrentLanguageFromStorage();

    this.logger.info('🎯 获取活跃组件信息', {
      target,
      sessionId,
      language,
      routingContext,
      currentScenario: routingState.currentScenario,
      isDOMVisible,
    });

    return { target, sessionId, language };
  }

  /**
   * 检查组件是否存在于DOM中
   */
  private isComponentVisibleInDOM(target: 'greeting-page' | 'custom-component'): boolean {
    try {
      if (target === 'greeting-page') {
        return document.querySelector('[data-component="greeting-page"]') !== null;
      } else {
        return document.querySelector('[data-component="chat-widget"]') !== null;
      }
    } catch (error) {
      this.logger.warn('检查组件DOM可见性失败', { target, error });
      return false;
    }
  }

  /**
   * 从localStorage获取当前语言设置
   */
  private getCurrentLanguageFromStorage(): 'mandarin' | 'chuanyu' {
    try {
      const savedLanguage = localStorage.getItem('websdk-language') as 'mandarin' | 'chuanyu';
      if (savedLanguage && (savedLanguage === 'mandarin' || savedLanguage === 'chuanyu')) {
        return savedLanguage;
      }
    } catch (error) {
      this.logger.warn('读取localStorage语言状态失败', { error });
    }
    return 'mandarin'; // 默认值
  }

  /**
   * 处理speakMode请求 - 通过HKSTT服务
   */
  private async handleSpeakMode(
    params: SpeakModeParams
  ): Promise<{ success: boolean; message?: string; speakMode?: number }> {
    const { speakMode } = params;

    this.logger.info('🎤 处理speakMode请求', { speakMode });

    // 验证参数
    if (typeof speakMode !== 'number' || (speakMode !== 0 && speakMode !== 1)) {
      throw new Error('speakMode参数必须是0（唇动收音）或1（点击收音）');
    }

    // 获取HKSTT客户端
    const hksttClient = this.serviceCoordinator.getHKSTTClient();
    if (!hksttClient) {
      throw new Error('HKSTT客户端未初始化');
    }

    if (!hksttClient.isConnected()) {
      throw new Error('HKSTT服务未连接');
    }

    try {
      // 1. 先停止当前收音会话（丢弃当前的收音）
      this.logger.info('🛑 切换收音模式前，先停止当前收音会话');
      await hksttClient.stopSession();
      this.logger.info('✅ 当前收音会话已停止');

      // 2. 设置新的收音模式
      this.logger.info('🎤 设置新的收音模式', { speakMode });
      await hksttClient.setSpeakMode(speakMode);

      this.logger.info('✅ speakMode请求发送成功', { speakMode });

      return {
        success: true,
        speakMode,
        message: `收音模式已设置为${speakMode === 0 ? '唇动收音' : '点击收音'}`,
      };
    } catch (error) {
      this.logger.error('❌ speakMode请求发送失败', { speakMode, error });
      throw new Error(`设置收音模式失败: ${(error as Error).message}`);
    }
  }

  /**
   * 处理clientUI请求
   * 业务按钮点击事件，用于通知客户端UI操作
   */
  private async handleClientUI(params: unknown): Promise<{ success: boolean; message: string }> {
    this.logger.info('🎯 处理clientUI请求', { params });

    // 验证参数格式
    if (!params || typeof params !== 'object') {
      throw new Error('clientUI方法的params必须是对象');
    }

    const p = params as Record<string, unknown>;
    if (!p.action || typeof p.action !== 'string') {
      throw new Error('clientUI方法的params.action必须是非空字符串');
    }

    if (p.name !== undefined && typeof p.name !== 'string') {
      throw new Error('clientUI方法的params.name必须是字符串');
    }

    // clientUI是一个通知性质的请求，主要用于记录和转发
    // 这里我们只需要确认收到并返回成功状态
    this.logger.info('✅ clientUI事件已处理', {
      action: p.action,
      name: p.name,
    });

    return {
      success: true,
      message: `clientUI事件已处理: ${p.action}`,
    };
  }

  /**
   * 销毁处理器
   */
  public destroy(): void {
    this.logger.info('🗑️ 销毁JSON-RPC业务逻辑处理器');
    // EventBus会自动清理事件监听器
  }
}
