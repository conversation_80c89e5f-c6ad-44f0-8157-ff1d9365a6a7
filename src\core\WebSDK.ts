/**
 * WebSDK - 轻量级SDK核心类
 * 职责：初始化、生命周期管理、API委托
 * 遵循轻量级、职责分离原则
 */

import { JsonRpcBusinessHandler } from '../jsonrpc/JsonRpcBusinessHandler';
import {
  JsonRpcNotificationManager,
  NotificationCallback,
} from '../jsonrpc/JsonRpcNotificationManager';
import { JsonRpcRequestManager } from '../jsonrpc/JsonRpcRequestManager';
import { MessageRouter } from '../routing/MessageRouter';
import { AIClient } from '../services/AIClient';
import { HKSTTClient } from '../services/HKSTTClient';
import { ServiceCoordinator, ServiceCoordinatorConfig } from '../services/ServiceCoordinator';
import { TransportManager } from '../transport/TransportManager';
import {
  WebSDKConfig,
  InternalWebSDKConfig,
  createInternalConfig,
  validateConfig,
} from '../types/config';
import {
  JsonRpcMessage,
  JsonRpcMessageOptions,
  BusinessButtonClickCallback,
  isClientUIRequest,
} from '../types/jsonrpc';
import { GreetingPageManager } from '../ui/GreetingPageManager';
import { ErrorHandler } from '../utils/ErrorHandler';
import { Logger, LogLevel } from '../utils/Logger';
import '../types/global';

import { EventBus } from './EventBus';
import { PageStateManager } from './PageStateManager';

/**
 * SDK状态（简化版）
 */
export interface SDKStatus {
  isInitialized: boolean;
  isConnected: boolean;
  isReady: boolean;
  connectionUrl: string;
}

/**
 * WebSDK主类 - 轻量级架构
 * 只负责：初始化、生命周期管理、API委托
 */
export class WebSDK {
  private static instance: WebSDK | null = null;

  private config: InternalWebSDKConfig;
  private logger: Logger;
  private isInitialized = false;
  private isConnected = false;
  private isReady = false;

  // 核心组件
  private eventBus!: EventBus;

  // JSON-RPC管理器（核心API）
  private requestManager!: JsonRpcRequestManager;
  private businessHandler!: JsonRpcBusinessHandler;
  private notificationManager!: JsonRpcNotificationManager;

  // 传输和服务客户端（基础设施）
  private transportManager!: TransportManager;
  private hksttClient!: HKSTTClient;
  private aiClient!: AIClient;

  // 业务逻辑组件（轻量级）
  private messageRouter!: MessageRouter;
  private serviceCoordinator!: ServiceCoordinator;
  private pageStateManager!: PageStateManager;

  // UI管理器（轻量级）
  private greetingPageManager!: GreetingPageManager;

  private constructor(config: WebSDKConfig) {
    // 验证并创建内部配置
    validateConfig(config);
    this.config = createInternalConfig(config);

    this.logger = Logger.getInstance({
      level: this.config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'WebSDK',
    });

    // 初始化核心组件
    this.initializeCore();
  }

  /**
   * 获取SDK实例（单例模式）
   */
  public static getInstance(config?: WebSDKConfig): WebSDK {
    if (!WebSDK.instance) {
      if (!config) {
        throw ErrorHandler.createValidationError('首次调用getInstance时必须提供配置');
      }
      WebSDK.instance = new WebSDK(config);
    }
    return WebSDK.instance;
  }

  /**
   * 重置SDK实例（主要用于测试）
   */
  public static resetInstance(): void {
    if (WebSDK.instance) {
      WebSDK.instance.destroy();
      WebSDK.instance = null;
    }
  }

  /**
   * 初始化核心组件
   */
  private initializeCore(): void {
    try {
      this.logger.info('开始初始化SDK核心组件');

      // 创建事件总线
      this.eventBus = new EventBus();

      // 创建传输管理器
      this.transportManager = new TransportManager(this.config, this.eventBus);

      // 创建服务客户端
      this.hksttClient = new HKSTTClient(
        {
          url: this.config.hksttUrl,
          debug: this.config.debug,
        },
        this.eventBus
      );
      this.aiClient = new AIClient(
        {
          url: this.config.aiServerUrl,
          debug: this.config.debug,
          deviceId: this.config.deviceId,
          organizationId: this.config.organizationId,
        },
        this.eventBus
      );

      // 创建业务逻辑组件（轻量级）
      this.messageRouter = new MessageRouter(
        {
          defaultScenario: 'custom-component',
          debug: this.config.debug,
        },
        this.eventBus
      );

      // 创建JSON-RPC管理器（模块化）
      this.requestManager = new JsonRpcRequestManager(this.eventBus);
      this.notificationManager = new JsonRpcNotificationManager(this.eventBus, this.messageRouter);
      // 构建ServiceCoordinator配置
      const coordinatorConfig: ServiceCoordinatorConfig = {
        debug: this.config.debug,
      };

      // 如果配置了TTS URL，则启用TTS服务
      if (this.config.ttsUrl) {
        coordinatorConfig.tts = {
          serverUrl: this.config.ttsUrl,
          protocol: 'http-sse', // 默认使用HTTP+SSE协议
          debug: this.config.debug,
        };
      }

      // 创建页面状态管理器
      this.pageStateManager = new PageStateManager(this.eventBus);

      this.serviceCoordinator = new ServiceCoordinator(
        coordinatorConfig,
        this.eventBus,
        this.hksttClient,
        this.aiClient,
        this.pageStateManager,
        this.messageRouter
      );

      // 创建JSON-RPC业务逻辑处理器（在ServiceCoordinator之后）
      this.businessHandler = new JsonRpcBusinessHandler(
        this.eventBus,
        this.aiClient,
        this.serviceCoordinator,
        this.messageRouter,
        this.pageStateManager
      );

      // 创建UI管理器（轻量级）
      this.greetingPageManager = new GreetingPageManager(this);

      // 绑定EventBus事件处理
      this.bindEventHandlers();

      this.isInitialized = true;
      this.logger.info('SDK核心组件初始化完成');
    } catch (error) {
      this.logger.error('SDK初始化失败', error);
      throw ErrorHandler.wrapError(error as Error);
    }
  }

  /**
   * 启动SDK（内部方法，仅供init函数使用）
   */
  public async _internalStart(): Promise<void> {
    if (!this.isInitialized) {
      throw ErrorHandler.createSystemError('SDK未初始化');
    }

    try {
      this.logger.info('启动SDK', {
        hksttUrl: this.config.hksttUrl,
        aiServerUrl: this.config.aiServerUrl,
      });

      // 启动传输管理器
      await this.transportManager.start();

      // 连接服务客户端
      await this.hksttClient.connect();
      // AI客户端不需要显式连接

      // 初始化已注册的组件
      this.initializeRegisteredComponents();

      this.isReady = true;
      this.eventBus.emit('sdk:ready');
      this.logger.info('SDK启动完成');
    } catch (error) {
      this.logger.error('SDK启动失败', error);
      throw ErrorHandler.wrapError(error as Error);
    }
  }

  /**
   * 停止SDK
   */
  public async stop(): Promise<void> {
    this.logger.info('停止SDK');

    try {
      // 断开服务客户端连接
      this.hksttClient.disconnect();
      // AI客户端不需要显式断开

      // 停止传输管理器
      this.transportManager.stop();

      this.isReady = false;
      this.isConnected = false;
      this.logger.info('SDK已停止');
    } catch (error) {
      this.logger.error('SDK停止失败', error);
      throw ErrorHandler.wrapError(error as Error);
    }
  }

  /**
   * 发送JSON-RPC消息（核心API）
   * 返回Promise但不维护长期pending状态
   * 客户软件负责pending请求管理
   */
  public async sendJsonRpcMessage(
    message: JsonRpcMessage,
    options?: JsonRpcMessageOptions
  ): Promise<unknown> {
    return this.requestManager.sendJsonRpcMessage(message, options);
  }

  /**
   * 注册JSON-RPC通知监听器
   */
  public onNotification(method: string, callback: NotificationCallback): void {
    this.notificationManager.onNotification(method, callback);
  }

  /**
   * 获取SDK状态（简化版）
   */
  public getStatus(): SDKStatus {
    return {
      isInitialized: this.isInitialized,
      isConnected: this.isConnected,
      isReady: this.isReady,
      connectionUrl: this.config.hksttUrl,
    };
  }

  /**
   * 获取事件总线（供外部组件使用）
   */
  public getEventBus(): EventBus {
    return this.eventBus;
  }

  /**
   * 获取SDK配置
   */
  public getConfig(): InternalWebSDKConfig {
    return this.config;
  }

  /**
   * 获取静态文件基础路径
   */
  public getStaticBasePath(): string {
    return this.config.staticBasePath;
  }

  /**
   * 获取当前TTS播放状态
   */
  public getTTSStatus(): string {
    if (this.serviceCoordinator) {
      const ttsService = this.serviceCoordinator.getTTSService();
      if (ttsService) {
        return ttsService.getStatus();
      }
    }
    return 'idle';
  }

  /**
   * 安全地注册事件监听器（确保SDK就绪后再注册）
   */
  public onReady(callback: () => void): void {
    if (this.isReady) {
      callback();
    } else {
      // 监听SDK就绪事件
      this.eventBus.on('sdk:ready', callback);
    }
  }

  /**
   * 监听业务按钮点击事件
   * @param callback 回调函数，接收完整的JSON-RPC请求对象
   */
  public onBusinessButtonClick(callback: BusinessButtonClickCallback): void {
    // 监听专门的业务按钮点击事件
    this.eventBus.on('business:button-click', (data: unknown) => {
      // 使用类型守卫：检查是否为clientUI请求
      if (isClientUIRequest(data)) {
        callback(data);
      }
    });
  }

  /**
   * 获取传输管理器（供外部组件使用）
   */
  public getTransportManager(): TransportManager {
    return this.transportManager;
  }

  /**
   * 获取HKSTT客户端（供外部组件使用）
   */
  public getHKSTTClient(): HKSTTClient {
    return this.hksttClient;
  }

  /**
   * 获取AI客户端（供外部组件使用）
   */
  public getAIClient(): AIClient {
    return this.aiClient;
  }

  /**
   * 显示打招呼页面（通过EventBus解耦）
   */
  public showGreetingPage(): void {
    this.logger.info('显示打招呼页面');
    this.eventBus.emit('greeting-page:show', {
      timestamp: Date.now(),
    });
  }

  /**
   * 隐藏打招呼页面（通过EventBus解耦）
   */
  public hideGreetingPage(): void {
    this.logger.info('隐藏打招呼页面');
    this.eventBus.emit('greeting-page:hide', {
      timestamp: Date.now(),
    });
  }

  /**
   * 获取消息路由器（用于调试和高级用法）
   */
  public getMessageRouter() {
    return this.messageRouter;
  }

  /**
   * 获取服务协调器（用于调试和高级用法）
   */
  public getServiceCoordinator() {
    return this.serviceCoordinator;
  }

  /**
   * 获取页面状态管理器（用于调试和高级用法）
   */
  public getPageStateManager() {
    return this.pageStateManager;
  }

  /**
   * 绑定EventBus事件处理器
   */
  private bindEventHandlers(): void {
    // 绑定打招呼页面显示/隐藏事件
    this.eventBus.on('greeting-page:show', () => {
      this.greetingPageManager.show();
    });

    this.eventBus.on('greeting-page:hide', () => {
      this.greetingPageManager.hide();
    });

    // 绑定连接状态监听
    this.eventBus.on('hkstt-client:state-change', (data: unknown) => {
      if (typeof data === 'object' && data !== null && 'state' in data) {
        const { state } = data as { state: string };
        const isConnected = state === 'connected';

        this.logger.info('HKSTT客户端状态变化', {
          state,
          isConnected,
          previousConnectionStatus: this.isConnected,
        });

        // 更新SDK连接状态
        if (this.isConnected !== isConnected) {
          this.isConnected = isConnected;

          // 发送连接状态变化事件
          this.eventBus.emit('connection:status', { connected: isConnected });

          this.logger.info('SDK连接状态已更新', { isConnected });
        }
      }
    });
  }

  /**
   * 初始化所有已注册的组件
   */
  private initializeRegisteredComponents(): void {
    if (!window.webSDKComponents) {
      this.logger.info('没有找到已注册的组件');
      return;
    }

    this.logger.info(`开始初始化 ${window.webSDKComponents.length} 个已注册的组件`);

    window.webSDKComponents.forEach((component, index) => {
      try {
        this.logger.info(
          `初始化组件 ${index + 1}/${window.webSDKComponents?.length || 0}: ${component.type}`
        );
        component.initialize(this);
      } catch (error) {
        this.logger.error(`组件 ${component.type} 初始化失败`, error);
      }
    });

    this.logger.info('所有组件初始化完成');
  }

  /**
   * 销毁SDK
   */
  public destroy(): void {
    this.logger.info('销毁SDK');

    // 销毁JSON-RPC管理器
    this.requestManager.destroy();
    this.businessHandler.destroy();
    this.notificationManager.destroy();

    // 销毁服务客户端
    this.hksttClient.destroy();
    this.aiClient.destroy();

    // 销毁传输管理器
    this.transportManager.destroy();

    // 事件总线不需要显式销毁

    this.isInitialized = false;
    this.isConnected = false;
    this.isReady = false;

    this.logger.info('SDK已销毁');
  }
}

/**
 * 初始化SDK的便捷函数
 */
export async function init(config: WebSDKConfig): Promise<WebSDK> {
  const sdk = WebSDK.getInstance(config);
  await sdk._internalStart();
  return sdk;
}

/**
 * 获取SDK实例的便捷函数（如果已初始化）
 */
export function getWebSDK(): WebSDK | null {
  try {
    return WebSDK.getInstance();
  } catch {
    return null;
  }
}
