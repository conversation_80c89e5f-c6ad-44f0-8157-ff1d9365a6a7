<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打招呼页面演示</title>
    <style>

    </style>
</head>

<body>
    <div class="container">
        <a href="../index.html" class="nav-link">← 返回首页</a>


        <button id="show-greeting-btn" class="main-btn" disabled>显示打招呼页面</button>
    </div>


    <!-- 加载SDK -->
    <script src="../dist/web-service-sdk.js"></script>
    <script>
        let sdk = null;
        // 处理新用户事件
        function handleNewUser(userData) {

            const shouldShow = confirm('检测到新用户进入，是否显示打招呼页面？');

            if (shouldShow) {
                window.sdk.showGreetingPage();
            } else {
                console.log('用户选择跳过打招呼页面');
            }
        }

        // 初始化SDK
        WebSDK.init({
            // hksttUrl: 'ws://*************:8001',
            // hksttUrl: 'ws://*************:20096',
            hksttUrl: 'ws://*************:20096',

            // aiServerUrl: 'http://*************:8000',
            aiServerUrl: 'http://*************:8000',
            ttsUrl: 'http://*************:8000',
            // 示例：自定义组织ID
            organizationId: 707,                  // 示例：自定义组织ID

            debug: true
        }).then(sdkInstance => {
            sdk = sdkInstance;
            window.sdk = sdk;
            sdk.onNotification('notifications/newUser', (params) => {
                console.log('%%%检测到新用户:', params);
                handleNewUser(params);
            });
            // 监听用户输入通知
            sdk.onNotification('notifications/userInput', (params) => {
                console.log('%%%用户输入:', params.userInput);
            });

            // 监听AI响应通知  
            sdk.onNotification('notifications/aiResponse', (params) => {
                console.log('%%%%AI响应:', params.message);
            });
            // 监听业务按钮点击事件
            sdk.onBusinessButtonClick((jsonRpcRequest) => {
                console.log('%%%%用户点击了业务按钮:', jsonRpcRequest);

            });
            // 监听状态通知
            sdk.onNotification('notifications/status', (params) => {
                console.log('%%%%状态:', params.message);
            });
            // 启用按钮
            document.getElementById('show-greeting-btn').disabled = false;

        }).catch(error => {
            console.error('SDK初始化失败:', error);
        });

        // 绑定按钮事件
        document.getElementById('show-greeting-btn').addEventListener('click', () => {
            if (sdk) {
                sdk.showGreetingPage();
            }
        });
    </script>
</body>

</html>