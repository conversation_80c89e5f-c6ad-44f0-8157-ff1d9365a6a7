/**
 * React版本的简化数字人视频组件
 * 与Lit版本功能保持一致，用于GreetingPage集成
 */

import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useImperativeHandle,
  forwardRef,
} from 'react';

import { Logger } from '../../utils/Logger';

/**
 * 计算从源URL到目标URL的相对路径
 */
function getRelativePath(from: URL, to: URL): string {
  // 如果协议或主机不同，返回绝对路径
  if (from.protocol !== to.protocol || from.host !== to.host) {
    return to.href;
  }

  // 分割路径
  const fromParts = from.pathname.split('/').filter(part => part !== '');
  const toParts = to.pathname.split('/').filter(part => part !== '');

  // 移除文件名（保留目录）
  if (fromParts.length > 0 && !from.pathname.endsWith('/')) {
    fromParts.pop();
  }

  // 找到共同的前缀
  let commonLength = 0;
  while (
    commonLength < fromParts.length &&
    commonLength < toParts.length &&
    fromParts[commonLength] === toParts[commonLength]
  ) {
    commonLength++;
  }

  // 计算需要向上的层数
  const upLevels = fromParts.length - commonLength;

  // 计算剩余的路径
  const remainingPath = toParts.slice(commonLength);

  // 构建相对路径
  const relativeParts = [];
  for (let i = 0; i < upLevels; i++) {
    relativeParts.push('..');
  }
  relativeParts.push(...remainingPath);

  return relativeParts.length > 0 ? relativeParts.join('/') : '.';
}

/**
 * 数字人状态枚举
 */
export enum DigitalHumanState {
  IDLE = 'idle',
  SPEAKING = 'speaking',
  MICROPHONE_BUTTON = 'microphone_button', // 按键收音模式
  MICROPHONE_LIP = 'microphone_lip', // 唇动收音模式
}

/**
 * 组件属性接口
 */
interface SimpleDigitalHumanReactProps {
  videoBasePath?: string;
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  width?: string | number;
  height?: string | number;
  className?: string;
  style?: React.CSSProperties;
  microphoneMode?: 'button' | 'lip'; // 收音模式：按键收音或唇动收音
  layoutMode?: 'standard' | 'care'; // 布局模式：标准模式或关爱模式（保留接口兼容性）
  usageContext?: 'greeting-page' | 'chat-widget'; // 使用场景：打招呼页面或聊天窗口
  sdk?:
  | {
    startRecording?: () => void;
    stopRecording?: () => void;
    getStatus?: () => { isReady: boolean };
    getTTSStatus?: () => string;
    getHKSTTClient?: () => {
      isConnected: () => boolean;
      startAudio: () => Promise<void>;
      endAudio: () => Promise<void>;
    };
    getEventBus?: () => {
      on: (event: string, callback: (data: unknown) => void) => void;
      off: (event: string, callback: (data: unknown) => void) => void;
      emit: (event: string, data?: unknown) => void;
    };
  }
  | undefined; // SDK实例，用于调用HKSTT方法
}

/**
 * 组件暴露的方法接口
 */
export interface SimpleDigitalHumanReactRef {
  switchMicrophoneMode: (mode: 'button' | 'lip') => void;
  exitMicrophoneMode: () => void;
  startSpeaking: () => void;
  stopSpeaking: () => void;
}

// 添加CSS动画样式
const animationStyles = `
  @keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
  }

  @keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
  }

  @keyframes recordingGlow {
    0% { box-shadow: 0 0 20px rgba(0, 122, 255, 0.6); }
    50% { box-shadow: 0 0 30px rgba(0, 122, 255, 0.9), 0 0 40px rgba(0, 122, 255, 0.4); }
    100% { box-shadow: 0 0 20px rgba(0, 122, 255, 0.6); }
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined' && !document.getElementById('digital-human-animations')) {
  const style = document.createElement('style');
  style.id = 'digital-human-animations';
  style.textContent = animationStyles;
  document.head.appendChild(style);
}

/**
 * React版本的简化数字人组件
 */
export const SimpleDigitalHumanReact = forwardRef<
  SimpleDigitalHumanReactRef,
  SimpleDigitalHumanReactProps
>(
  (
    {
      videoBasePath = '',
      autoPlay = true,
      loop = true,
      width = '100%',
      height = '100%',
      className = '',
      style = {},
      microphoneMode = 'button',
      layoutMode: _layoutMode = 'standard', // 保留参数以维持接口兼容性，实际间距控制已移至外层容器
      usageContext = 'greeting-page', // 默认为打招呼页面，保持向后兼容
      sdk,
    },
    ref
  ) => {
    // 状态管理
    const [currentState, setCurrentState] = useState<DigitalHumanState>(DigitalHumanState.IDLE);
    const [currentVideoSrc, setCurrentVideoSrc] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [microphoneVideoSrc, setMicrophoneVideoSrc] = useState(''); // 收音模式视频源

    // 按键收音状态管理
    const [isRecording, setIsRecording] = useState(false);
    const [isRecordingLoading, setIsRecordingLoading] = useState(false);
    const [recordingError, setRecordingError] = useState<string | null>(null);
    // 新增：用户授权录音状态 - 跟踪用户是否已明确点击开始按钮
    const [userAuthorizedRecording, setUserAuthorizedRecording] = useState(false);

    // Refs
    const loggerRef = useRef(Logger.getInstance({ prefix: 'SimpleDigitalHumanReact' }));
    const waitVideosRef = useRef<string[]>([]);
    const speakVideosRef = useRef<string[]>([]);
    const microphoneVideosRef = useRef<{ button: string; lip: string }>({ button: '', lip: '' });
    const currentVideoIndexRef = useRef(0);
    const playTimerRef = useRef<number | null>(null);
    const retryCountRef = useRef(0); // 添加重试计数器
    const maxRetryCount = 3; // 最大重试次数

    /**
     * 获取SDK静态资源基础路径
     */
    const getSDKStaticBasePath = useCallback((): string => {
      // 尝试从当前脚本路径推断SDK路径
      const scripts = document.querySelectorAll('script[src]');
      for (const script of scripts) {
        const src = (script as HTMLScriptElement).src;
        if (src.includes('web-service-sdk.js')) {
          try {
            // 获取脚本的完整URL
            const scriptUrl = new URL(src, window.location.href);

            // 计算SDK所在目录的URL
            const sdkDirUrl = new URL('.', scriptUrl);

            // 计算静态资源目录的URL
            const staticUrl = new URL('static', sdkDirUrl);

            // 如果是file://协议，返回相对路径
            if (window.location.protocol === 'file:') {
              // 计算从当前页面到静态资源的相对路径
              const currentPageUrl = new URL(window.location.href);
              const relativePath = getRelativePath(currentPageUrl, staticUrl);
              return relativePath;
            }

            // 对于http://协议，返回相对于当前页面的路径
            const currentPageUrl = new URL(window.location.href);
            const relativePath = getRelativePath(currentPageUrl, staticUrl);
            return relativePath;
          } catch {
            // 解析失败时使用备用方案
          }
        }
      }

      // 备用方案：使用相对路径
      return './static';
    }, []);

    /**
     * 初始化视频资源路径
     */
    const initializeVideoPaths = useCallback(() => {
      // 如果没有指定路径，使用默认的SDK静态资源路径
      const basePath = videoBasePath || getSDKStaticBasePath() + '/human';
      const staticBasePath = getSDKStaticBasePath();

      // 等待状态视频
      waitVideosRef.current = [
        `${basePath}/wait/2.5Ddaiji1.webp`,
        `${basePath}/wait/2.5Ddaiji2.webp`,
        `${basePath}/wait/2.5Ddaiji3.webp`,
      ];

      // 说话状态视频
      speakVideosRef.current = [
        `${basePath}/speak/speak1.webp`,
        `${basePath}/speak/speak2.webp`,
        `${basePath}/speak/speak3.webp`,
        `${basePath}/speak/speak4.webp`,
      ];

      // 收音模式视频
      microphoneVideosRef.current = {
        button: `${staticBasePath}/microphone/button.webp`,
        lip: `${staticBasePath}/microphone/lips.webp`,
      };
    }, [videoBasePath, getSDKStaticBasePath]);

    /**
     * 播放下一个视频
     */
    const playNextVideo = useCallback(() => {
      const logger = loggerRef.current;
      logger.debug('🎬 开始播放下一个视频', {
        currentState,
        microphoneMode,
        waitVideosCount: waitVideosRef.current.length,
        speakVideosCount: speakVideosRef.current.length,
      });

      // 根据当前状态选择视频
      if (currentState === DigitalHumanState.MICROPHONE_BUTTON) {
        // 按键收音模式：显示收音视频覆盖层，确保有背景视频
        logger.info('🎤 切换到按键收音模式');
        setMicrophoneVideoSrc(microphoneVideosRef.current.button);

        // 确保有主视频作为背景
        if (!currentVideoSrc && waitVideosRef.current.length > 0) {
          const waitVideo = waitVideosRef.current[0]; // 使用第一个等待视频
          if (waitVideo) {
            logger.info('🎤 按键收音模式设置背景视频', { waitVideo });
            setCurrentVideoSrc(waitVideo);
          }
        }
        return;
      } else if (currentState === DigitalHumanState.MICROPHONE_LIP) {
        // 唇动收音模式：显示收音视频覆盖层，确保有背景视频
        logger.info('👄 切换到唇动收音模式');
        setMicrophoneVideoSrc(microphoneVideosRef.current.lip);

        // 确保有主视频作为背景
        if (!currentVideoSrc && waitVideosRef.current.length > 0) {
          const waitVideo = waitVideosRef.current[0]; // 使用第一个等待视频
          if (waitVideo) {
            logger.info('👄 唇动收音模式设置背景视频', { waitVideo });
            setCurrentVideoSrc(waitVideo);
          }
        }
        return;
      }
      // 注意：移除了自动清除收音视频的逻辑
      // 收音模式视频的显示/隐藏应该只由收音模式的切换函数控制
      // 这样可以避免TTS播放时意外清除收音模式按钮

      // 原有的等待和说话状态逻辑
      const videoList =
        currentState === DigitalHumanState.SPEAKING
          ? speakVideosRef.current
          : waitVideosRef.current;

      if (videoList.length === 0) {
        logger.warn('⚠️ 视频列表为空，无法播放', {
          currentState,
          waitVideosCount: waitVideosRef.current.length,
          speakVideosCount: speakVideosRef.current.length,
        });
        return;
      }

      // 减少重复的视频选择日志
      if (process.env.NODE_ENV === 'development') {
        logger.info('🎬 选择视频列表', {
          currentState,
          videoListType: currentState === DigitalHumanState.SPEAKING ? 'speaking' : 'waiting',
          videoCount: videoList.length,
        });
      }

      // 随机选择视频（避免连续播放同一个视频）
      let nextIndex;
      if (videoList.length === 1) {
        nextIndex = 0;
      } else {
        do {
          nextIndex = Math.floor(Math.random() * videoList.length);
        } while (nextIndex === currentVideoIndexRef.current && videoList.length > 1);
      }

      currentVideoIndexRef.current = nextIndex;
      const videoSrc = videoList[nextIndex] || '';

      logger.debug('🎯 选中视频', {
        index: nextIndex,
        fileName: videoSrc.split('/').pop(),
        fullPath: videoSrc,
      });

      if (!videoSrc) {
        logger.error('❌ 视频源为空');
        return;
      }

      // 简单直接的切换
      setIsLoading(true);
      setCurrentVideoSrc(videoSrc);
    }, [currentState, currentVideoSrc, microphoneMode]);

    /**
     * 安排下一个视频播放（固定10秒）
     */
    const scheduleNextVideo = useCallback(() => {
      // 清除之前的定时器
      if (playTimerRef.current) {
        clearTimeout(playTimerRef.current);
      }

      if (!loop) return;

      // 收音模式视频不需要循环播放，持续显示
      if (
        currentState === DigitalHumanState.MICROPHONE_BUTTON ||
        currentState === DigitalHumanState.MICROPHONE_LIP
      ) {
        return;
      }

      // 所有视频固定10秒播放时长
      playTimerRef.current = window.setTimeout(() => {
        playNextVideo();
      }, 10000);
    }, [loop, playNextVideo, currentState]);

    /**
     * 切换收音模式
     */
    const switchMicrophoneMode = useCallback(
      (mode: 'button' | 'lip') => {
        const logger = loggerRef.current;
        logger.info('🎤 切换收音模式', {
          mode,
          currentState,
          currentVideoSrc,
          hasMainVideo: !!currentVideoSrc,
        });

        const newState =
          mode === 'button'
            ? DigitalHumanState.MICROPHONE_BUTTON
            : DigitalHumanState.MICROPHONE_LIP;

        // 确保主视频继续播放，只切换收音视频覆盖层
        const videoSrc =
          mode === 'button' ? microphoneVideosRef.current.button : microphoneVideosRef.current.lip;

        // 如果没有主视频，先设置一个背景视频
        if (!currentVideoSrc && waitVideosRef.current.length > 0) {
          const waitVideo = waitVideosRef.current[0]; // 使用第一个等待视频作为背景
          if (waitVideo) {
            logger.info('🎤 切换收音模式时设置背景视频', { waitVideo, mode });
            setCurrentVideoSrc(waitVideo);
          }
        }

        // 设置收音视频，再更新状态
        setMicrophoneVideoSrc(videoSrc);
        setCurrentState(newState);

        logger.info('🎤 收音模式切换完成', {
          newState,
          microphoneVideoSrc: videoSrc,
          mainVideoSrc: currentVideoSrc,
        });
      },
      [currentState, currentVideoSrc]
    );

    /**
     * 退出收音模式，回到正常状态
     */
    const exitMicrophoneMode = useCallback(() => {
      loggerRef.current.info('🔇 退出收音模式，回到等待状态');
      setCurrentState(DigitalHumanState.IDLE);
      setMicrophoneVideoSrc(''); // 清除收音视频
      // 重置录音状态
      setIsRecording(false);
      setIsRecordingLoading(false);
    }, []);

    /**
     * 处理按键收音按钮点击
     */
    const handleMicrophoneButtonClick = useCallback(async () => {
      const logger = loggerRef.current;

      logger.info('🎤 按键收音按钮被点击', {
        sdkExists: !!sdk,
        sdkReady: sdk?.getStatus?.()?.isReady,
        isRecording,
        isRecordingLoading,
      });

      if (!sdk) {
        logger.warn('SDK实例不存在，无法进行按键收音操作');
        setRecordingError('SDK未初始化');
        return;
      }

      if (!sdk.getStatus?.()?.isReady) {
        logger.warn('SDK未就绪，无法进行按键收音操作');
        setRecordingError('SDK未就绪');
        return;
      }

      // 检查HKSTT客户端连接状态
      const hksttClient = sdk.getHKSTTClient?.();
      logger.info('🔍 检查HKSTT客户端状态', {
        hksttClientExists: !!hksttClient,
        isConnected: hksttClient?.isConnected(),
      });

      if (!hksttClient?.isConnected()) {
        logger.warn('HKSTT客户端未连接，等待自动重连');
        setRecordingError('语音服务未连接，请稍后重试');
        return;
      }

      if (isRecordingLoading) {
        logger.info('录音操作进行中，忽略重复点击');
        return;
      }

      try {
        setIsRecordingLoading(true);
        setRecordingError(null); // 清除之前的错误

        if (!isRecording) {
          // 开始录音
          logger.info('🎤 开始按键收音');
          await hksttClient?.startAudio();
          setIsRecording(true);
          // 🆕 设置用户授权状态 - 用户已明确点击开始按钮
          setUserAuthorizedRecording(true);
          logger.info('✅ 按键收音已启动，用户已授权');
        } else {
          // 结束录音
          logger.info('🛑 结束按键收音');
          await hksttClient?.endAudio();
          setIsRecording(false);
          // 🆕 清除用户授权状态 - 录音结束，需要重新授权
          setUserAuthorizedRecording(false);
          logger.info('✅ 按键收音已结束，用户授权已清除');
        }
      } catch (error) {
        logger.error('按键收音操作失败', error);
        // 发生错误时重置状态
        setIsRecording(false);
        // 🆕 发生错误时也清除用户授权状态
        setUserAuthorizedRecording(false);

        // 设置用户友好的错误信息
        const errorMessage = error instanceof Error ? error.message : '网络连接异常';
        setRecordingError(errorMessage);

        // 3秒后自动清除错误信息
        setTimeout(() => {
          setRecordingError(null);
        }, 3000);
      } finally {
        setIsRecordingLoading(false);
      }
    }, [sdk, isRecording, isRecordingLoading]);

    /**
     * 监听ASR结果通知，自动结束录音
     */
    useEffect(() => {
      if (!sdk) return;

      const logger = loggerRef.current;

      // 监听ASR离线识别结果通知
      const handleAsrOfflineResult = (data: unknown) => {
        const asrData = data as { text?: string };
        logger.info('🎤 收到ASR离线识别结果', {
          usageContext,
          microphoneMode,
          isRecording,
          hasText: !!asrData.text,
          textLength: asrData.text?.length || 0,
        });

        // 只有在按键收音模式且正在录音时才自动停止
        if (microphoneMode === 'button' && isRecording) {
          logger.info('🛑 ASR结果触发自动停止按键收音', {
            text: asrData.text?.substring(0, 50) + '...',
          });

          // 自动停止录音状态
          setIsRecording(false);
          setIsRecordingLoading(false);
          // 🆕 ASR结果触发停止时也清除用户授权状态
          setUserAuthorizedRecording(false);

          // 可选：调用HKSTT客户端的endAudio方法确保服务端也停止录音
          if (sdk && sdk.getHKSTTClient && sdk.getHKSTTClient().isConnected()) {
            sdk
              .getHKSTTClient()
              .endAudio()
              .catch((error: unknown) => {
                logger.warn('自动停止录音时调用endAudio失败', error);
              });
          }

          logger.info('✅ 按键收音已自动结束（ASR结果触发），用户授权已清除');
        } else {
          logger.debug('跳过自动停止录音', {
            reason: microphoneMode !== 'button' ? '非按键收音模式' : '当前未在录音',
            microphoneMode,
            isRecording,
          });
        }
      };

      // 监听录音状态查询事件（用于页面切换时的资源清理）
      const handleRecordingStatusQuery = (data: unknown) => {
        const queryData = data as {
          callback?: (status: { isRecording: boolean; isRecordingLoading: boolean }) => void;
        };
        logger.info('🔍 收到录音状态查询', {
          usageContext,
          isRecording,
          isRecordingLoading,
          hasCallback: !!(queryData.callback && typeof queryData.callback === 'function'),
        });

        // 只有打招呼页面的数字人组件才响应录音状态查询
        // 因为录音功能主要在打招呼页面使用
        if (usageContext === 'greeting-page') {
          if (queryData.callback && typeof queryData.callback === 'function') {
            queryData.callback({ isRecording, isRecordingLoading });
            logger.info('📋 已响应录音状态查询（打招呼页面）', { isRecording });
          } else {
            logger.warn('⚠️ 录音状态查询事件缺少回调函数');
          }
        } else {
          logger.info('📝 跳过录音状态查询（非打招呼页面组件）', { usageContext });
        }
      };

      // 注册事件监听器
      const eventBus = sdk.getEventBus?.();
      // 🆕 处理用户授权状态查询
      const handleUserAuthorizationQuery = (data: unknown) => {
        const queryData = data as { requestId?: string; usageContext?: string };
        logger.debug('🔍 收到用户授权状态查询', {
          requestId: queryData.requestId,
          usageContext: queryData.usageContext,
          currentUsageContext: usageContext,
          userAuthorizedRecording,
          microphoneMode,
        });

        // 只响应与当前使用场景匹配的查询
        if (queryData.usageContext === usageContext || !queryData.usageContext) {
          eventBus?.emit('user-authorization:status-response', {
            requestId: queryData.requestId,
            usageContext,
            userAuthorizedRecording,
            microphoneMode,
            isRecording,
          });
        }
      };

      // 监听HKSTT的ASR离线识别结果事件
      if (eventBus) {
        eventBus.on('hkstt:asr-offline-result', handleAsrOfflineResult);
        eventBus.on('recording:status-query', handleRecordingStatusQuery);
        eventBus.on('user-authorization:status-query', handleUserAuthorizationQuery);
      }

      // 清理函数
      return () => {
        if (eventBus) {
          eventBus.off('hkstt:asr-offline-result', handleAsrOfflineResult);
          eventBus.off('recording:status-query', handleRecordingStatusQuery);
          eventBus.off('user-authorization:status-query', handleUserAuthorizationQuery);
        }
      };
    }, [sdk, isRecording, microphoneMode, isRecordingLoading, usageContext]);

    /**
     * 处理视频加载完成
     */
    const handleVideoLoad = useCallback(
      (event: React.SyntheticEvent<HTMLImageElement>) => {
        const logger = loggerRef.current;
        // const loadedSrc = (event.target as HTMLImageElement).src;

        // 重置重试计数器（视频加载成功）
        retryCountRef.current = 0;
        // 移除setIsLoading(false)，避免影响视频显示

        // 如果启用循环播放，基于动画完成来切换下一个视频
        if (loop) {
          const img = event.target as HTMLImageElement;

          // 尝试监听动画结束事件（某些浏览器支持）
          const handleAnimationEnd = () => {
            logger.info('🔄 动画结束，切换下一个视频');
            playNextVideo();
            img.removeEventListener('animationend', handleAnimationEnd);
          };

          // 添加动画结束监听器
          img.addEventListener('animationend', handleAnimationEnd);

          // 备用方案：使用定时器（防止某些浏览器不支持animationend事件）
          scheduleNextVideo();
        }
      },
      [loop, scheduleNextVideo, playNextVideo]
    );

    /**
     * 处理视频加载错误
     */
    const handleVideoError = useCallback(
      (event: React.SyntheticEvent<HTMLImageElement>) => {
        const logger = loggerRef.current;
        const failedSrc = (event.target as HTMLImageElement).src;
        const currentRetryCount = retryCountRef.current;

        logger.error('❌ 数字人视频加载失败', {
          src: failedSrc,
          currentState,
          microphoneMode,
          retryCount: currentRetryCount,
          maxRetryCount,
          error: event,
        });

        // 移除setIsLoading(false)，避免影响视频显示

        // 通用重试机制：对所有视频都进行重试
        if (currentRetryCount < maxRetryCount) {
          retryCountRef.current += 1;
          logger.info('🔄 重试加载视频', {
            retryCount: retryCountRef.current,
            src: failedSrc,
          });

          setTimeout(() => {
            setCurrentVideoSrc(''); // 清空当前视频
            setTimeout(() => {
              setCurrentVideoSrc(failedSrc); // 重新设置视频源
            }, 100);
          }, 500);
          return;
        }

        // 重置重试计数器
        retryCountRef.current = 0;

        // 如果重试失败，尝试播放下一个视频
        setTimeout(() => {
          logger.info('🔄 重试失败，尝试播放下一个视频');
          playNextVideo();
        }, 1000);
      },
      [playNextVideo, currentState, microphoneMode, maxRetryCount]
    );

    // 极简初始化 - 移除所有可能导致循环的逻辑
    useEffect(() => {
      const logger = loggerRef.current;
      logger.info('🚀 数字人组件初始化');

      // 初始化视频资源路径
      initializeVideoPaths();

      // 查询当前TTS播放状态并同步数字人状态
      if (sdk && sdk.getTTSStatus) {
        const currentTTSStatus = sdk.getTTSStatus();
        logger.info('🔍 组件挂载时查询当前TTS播放状态', {
          currentTTSStatus,
          usageContext,
        });

        // 根据TTS状态设置数字人状态
        if (currentTTSStatus === 'playing') {
          logger.info('🎵 检测到TTS正在播放，切换到说话状态');
          setCurrentState(DigitalHumanState.SPEAKING);

          // 立即切换到speak视频
          const speakVideos = speakVideosRef.current;
          if (speakVideos.length > 0) {
            const randomIndex = Math.floor(Math.random() * speakVideos.length);
            const speakVideo = speakVideos[randomIndex];
            if (speakVideo) {
              logger.info('🎵 同步TTS状态，切换到speak视频', { speakVideo });
              setIsLoading(true);
              setCurrentVideoSrc(speakVideo);
            }
          }
        } else {
          logger.info('🔇 TTS未播放，保持空闲状态', { currentTTSStatus });
        }
      }

      // 如果autoPlay为true，设置第一个等待视频（仅在TTS未播放时）
      if (autoPlay) {
        setTimeout(() => {
          // 再次检查TTS状态，避免覆盖正在播放的状态
          const ttsStatus = sdk && sdk.getTTSStatus ? sdk.getTTSStatus() : 'idle';
          if (ttsStatus !== 'playing') {
            const waitVideos = [
              `${getSDKStaticBasePath()}/human/wait/2.5Ddaiji1.webp`,
              `${getSDKStaticBasePath()}/human/wait/2.5Ddaiji2.webp`,
              `${getSDKStaticBasePath()}/human/wait/2.5Ddaiji3.webp`,
            ];

            if (waitVideos[0]) {
              setCurrentVideoSrc(waitVideos[0]);
              logger.info('🎬 设置初始视频', { video: waitVideos[0] });
            }
          }
        }, 100);
      }

      // 简单的清理函数
      return () => {
        logger.info('🧹 数字人组件清理');
        if (playTimerRef.current) {
          clearTimeout(playTimerRef.current);
          playTimerRef.current = null;
        }
      };
    }, []); // eslint-disable-line react-hooks/exhaustive-deps -- autoPlay、getSDKStaticBasePath、initializeVideoPaths在组件生命周期中不会改变

    // 简化状态切换逻辑 - 移除复杂的视频切换
    useEffect(() => {
      // 只处理收音模式的视频设置
      if (
        currentState === DigitalHumanState.MICROPHONE_BUTTON ||
        currentState === DigitalHumanState.MICROPHONE_LIP
      ) {
        const videoSrc =
          currentState === DigitalHumanState.MICROPHONE_BUTTON
            ? microphoneVideosRef.current.button
            : microphoneVideosRef.current.lip;

        if (videoSrc) {
          setMicrophoneVideoSrc(videoSrc);
        }
      }
    }, [currentState]);

    // 简化收音模式监听
    useEffect(() => {
      if (
        currentState === DigitalHumanState.MICROPHONE_BUTTON ||
        currentState === DigitalHumanState.MICROPHONE_LIP
      ) {
        const videoSrc =
          microphoneMode === 'button'
            ? microphoneVideosRef.current.button
            : microphoneVideosRef.current.lip;

        if (videoSrc) {
          setMicrophoneVideoSrc(videoSrc);
        }
      }
    }, [microphoneMode, currentState]);

    // 轻量化TTS事件监听 - 直接监听DOM事件，避免复杂的依赖注入
    useEffect(() => {
      const logger = loggerRef.current;

      // TTS播放开始事件监听器
      const handleTTSPlayStart = () => {
        logger.info('🎵 监听到TTS播放开始，切换到说话状态');
        setCurrentState(DigitalHumanState.SPEAKING);
        // 立即切换到speak视频，不依赖状态更新
        const speakVideos = speakVideosRef.current;
        if (speakVideos.length > 0) {
          const randomIndex = Math.floor(Math.random() * speakVideos.length);
          const speakVideo = speakVideos[randomIndex];
          if (speakVideo) {
            // 减少TTS状态切换的重复日志
            if (process.env.NODE_ENV === 'development') {
              logger.info('🎵 立即切换到speak视频', {
                speakVideo,
                speakVideosCount: speakVideos.length,
              });
            }
            setIsLoading(true);
            setCurrentVideoSrc(speakVideo);
          }
        }
      };

      // TTS播放结束事件监听器
      const handleTTSPlayEnd = () => {
        logger.info('🔇 监听到TTS播放结束，切换到等待状态');
        setCurrentState(DigitalHumanState.IDLE);
        // 立即切换回等待视频，不依赖状态更新
        const waitVideos = waitVideosRef.current;
        if (waitVideos.length > 0) {
          const randomIndex = Math.floor(Math.random() * waitVideos.length);
          const waitVideo = waitVideos[randomIndex];
          if (waitVideo) {
            // 减少TTS状态切换的重复日志
            if (process.env.NODE_ENV === 'development') {
              logger.info('🔇 立即切换到wait视频', {
                waitVideo,
                waitVideosCount: waitVideos.length,
              });
            }
            setIsLoading(true);
            setCurrentVideoSrc(waitVideo);
          }
        }
      };

      // 监听全局TTS事件（通过DOM事件）
      document.addEventListener('tts:play-start', handleTTSPlayStart);
      document.addEventListener('tts:play-end', handleTTSPlayEnd);

      // 清理事件监听器
      return () => {
        document.removeEventListener('tts:play-start', handleTTSPlayStart);
        document.removeEventListener('tts:play-end', handleTTSPlayEnd);
      };
    }, []); // 空依赖数组，只在组件挂载时设置一次

    // 暴露方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        switchMicrophoneMode,
        exitMicrophoneMode,
        startSpeaking: () => {
          loggerRef.current.info('🎵 外部调用startSpeaking');
          setCurrentState(DigitalHumanState.SPEAKING);
        },
        stopSpeaking: () => {
          loggerRef.current.info('🔇 外部调用stopSpeaking');
          setCurrentState(DigitalHumanState.IDLE);
        },
      }),
      [switchMicrophoneMode, exitMicrophoneMode]
    );

    // 组件样式
    const containerStyle: React.CSSProperties = {
      width,
      height,
      position: 'relative',
      overflow: 'hidden',
      borderRadius: '8px',
      background: '#f8fafc', // 改为浅灰色背景，避免白屏问题
      display: 'flex',
      alignItems: 'flex-start', // 改为顶部对齐，避免居中产生的空隙
      justifyContent: 'center',
      // 移除内部的paddingTop设置，由外层容器控制间距
      ...style,
    };

    // 修复数字人显示问题：始终保持固定大小，不因收音模式变化
    const videoStyle: React.CSSProperties = {
      width: '100%',
      height: '100%', // 始终占满全部高度，保持数字人位置固定
      objectFit: 'contain', // 改为contain确保完整显示，避免截断
      borderRadius: 'inherit',
      backgroundColor: 'transparent', // 透明背景
      display: 'block', // 确保显示
    };

    // 收音模式视频样式 - 根据模式和使用场景调整位置和大小
    const getMicrophoneVideoStyle = (mode: 'button' | 'lip'): React.CSSProperties => {
      // 根据使用场景确定尺寸
      const getSizeByContext = () => {
        if (usageContext === 'chat-widget') {
          // ChatWidget场景：更小的尺寸
          return mode === 'button'
            ? { width: '60px', height: '60px' }
            : { width: '120px', height: '33px' }; // 120 * (245/900) ≈ 33px
        } else {
          // 打招呼页面场景：增大尺寸，使按钮更明显
          return mode === 'button'
            ? { width: '100px', height: '100px' } // 从80px增加到100px
            : { width: '240px', height: '65px' }; // 240 * (245/900) ≈ 65px，保持比例
        }
      };

      const size = getSizeByContext();

      if (mode === 'button') {
        // 按键收音模式：定位在数字人视频的中间右侧位置
        return {
          position: 'absolute',
          width: size.width,
          height: size.height,
          borderRadius: '0px', // 移除圆角
          objectFit: 'contain', // 确保内容完整显示
          zIndex: 15,
          border: 'none', // 移除边框
          transition: 'opacity 0.3s ease-in-out',
          top: '50%',
          right: '10px',
          transform: 'translateY(-50%)',
          background: 'transparent', // 确保背景透明
          boxShadow: 'none', // 移除阴影
        };
      } else {
        // 唇动收音模式：定位在数字人视频下方，作为完全透明的遮罩层
        return {
          position: 'absolute',
          width: size.width,
          height: size.height,
          borderRadius: '0px', // 移除圆角
          objectFit: 'contain',
          zIndex: 15, // 确保在数字人上方
          border: 'none', // 移除边框
          transition: 'opacity 0.3s ease-in-out',
          bottom: '20px', // 调整位置
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'transparent', // 完全透明背景
          boxShadow: 'none', // 移除阴影
        };
      }
    };

    return (
      <div className={className} style={containerStyle}>
        {/* 主数字人视频 */}
        {currentVideoSrc ? (
          <img
            style={videoStyle}
            src={currentVideoSrc}
            alt="数字人视频"
            onLoad={handleVideoLoad}
            onError={handleVideoError}
          />
        ) : (
          <div
            style={{
              ...videoStyle,
              background: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#64748b',
              fontSize: '14px',
              fontWeight: '500',
            }}
          >
            {isLoading ? '加载中...' : '数字人'}
          </div>
        )}

        {/* 收音模式视频覆盖层 - 按键收音模式下可点击 */}
        {microphoneVideoSrc && (
          <>
            {microphoneMode === 'button' ? (
              <button
                style={{
                  ...getMicrophoneVideoStyle(microphoneMode),
                  cursor: isRecordingLoading ? 'wait' : 'pointer',
                  opacity: isRecordingLoading ? 0.7 : 1,
                  filter: isRecording ? 'brightness(1.2) saturate(1.3)' : 'none',
                  transition: 'all 0.3s ease',
                  border: 'none',
                  padding: 0,
                  background: 'transparent',
                  borderRadius: '12px',
                  // 录音状态光效
                  boxShadow: isRecording ? '0 0 20px rgba(0, 122, 255, 0.6)' : 'none',
                  animation: isRecording ? 'recordingGlow 2s ease-in-out infinite' : 'none',
                }}
                onClick={handleMicrophoneButtonClick}
                disabled={isRecordingLoading}
                title={
                  isRecordingLoading ? '处理中...' : isRecording ? '点击结束录音' : '点击开始录音'
                }
              >
                <img
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    pointerEvents: 'none',
                  }}
                  src={microphoneVideoSrc}
                  alt={isRecording ? '录音中' : '点击开始录音'}
                />

                {/* 录音状态指示器 */}
                {isRecording && (
                  <div
                    style={{
                      position: 'absolute',
                      top: '8px',
                      right: '8px',
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      backgroundColor: '#ff4444',
                      animation: 'pulse 1.5s infinite',
                      boxShadow: '0 0 8px rgba(255, 68, 68, 0.6)',
                      zIndex: 20,
                    }}
                  />
                )}

                {/* 加载状态指示器 */}
                {isRecordingLoading && (
                  <div
                    style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      width: '24px',
                      height: '24px',
                      border: '3px solid rgba(255, 255, 255, 0.3)',
                      borderTop: '3px solid #007AFF',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite',
                      zIndex: 20,
                    }}
                  />
                )}

                {/* 错误信息提示 */}
                {recordingError && (
                  <div
                    style={{
                      position: 'absolute',
                      bottom: '8px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      backgroundColor: 'rgba(255, 68, 68, 0.9)',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      whiteSpace: 'nowrap',
                      zIndex: 20,
                      maxWidth: '90%',
                      textAlign: 'center',
                    }}
                  >
                    {recordingError}
                  </div>
                )}
              </button>
            ) : (
              <img
                style={getMicrophoneVideoStyle(microphoneMode)}
                src={microphoneVideoSrc}
                alt="收音模式视频"
              />
            )}
          </>
        )}
      </div>
    );
  }
);

SimpleDigitalHumanReact.displayName = 'SimpleDigitalHumanReact';
