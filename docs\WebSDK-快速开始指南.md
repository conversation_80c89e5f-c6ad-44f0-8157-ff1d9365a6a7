# WebSDK 快速开始指南

## 🚀 5分钟快速上手

参考现有示例，快速集成WebSDK到您的应用中。

## 📁 部署文件

将编译好的WebSDK文件放到您的静态资源目录：

```
您的应用/
├── static/
│   └── websdk/
│       └── websdk.min.js
└── pages/
    └── ai-page.html
```

## 💻 基础示例

参考 `examples/chat-demo.html`：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI助手</title>
</head>
<body>
    <!-- 聊天组件 -->
    <chat-widget style="display: none;"></chat-widget>

    <!-- 加载SDK -->
    <script src="/static/websdk/websdk.min.js"></script>
    <script>
        let sdk = null;

        // 初始化SDK
        WebSDK.init({
            hksttUrl: 'ws://your-server:8001',
            aiServerUrl: 'http://your-server:8080',
            deviceId: 'your-device-id',        // 可选，默认为"1234567890"
            organizationId: 206,               // 可选，默认为206
            debug: true
        }).then(sdkInstance => {
            sdk = sdkInstance;
            window.sdk = sdk;

            // 监听用户输入
            sdk.onNotification('notifications/userInput', (params) => {
                console.log('用户输入:', params.userInput);
            });

            // 监听AI响应
            sdk.onNotification('notifications/aiResponse', (params) => {
                console.log('AI响应:', params.message);
            });

            // 监听业务按钮点击
            sdk.onBusinessButtonClick((jsonRpcRequest) => {
                console.log('业务按钮点击:', jsonRpcRequest);
            });

        }).catch(error => {
            console.error('SDK初始化失败:', error);
        });
    </script>
</body>
</html>
```

## 🔧 常用功能

### 1. 显示欢迎页面

参考 `examples/greeting-demo.html`：

```javascript
// 显示欢迎页面
sdk.showGreetingPage();

// 监听业务按钮点击
sdk.onBusinessButtonClick((jsonRpcRequest) => {
    console.log('用户点击了业务按钮:', jsonRpcRequest);
    // 处理业务逻辑
});
```

### 2. AI语音播报

```javascript
// 发送语音播报
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: {
        text: '您好，欢迎使用银行服务',
        delay: 1000,    // 延时1秒播报（可选）
        display: true   // 显示在聊天气泡（可选，默认true）
    },
    id: 'speak-1'
});
```

### 3. 其他常用功能

```javascript
// 切换收音模式：0=唇动收音，1=点击收音
// 注意：切换时会自动停止当前收音会话
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speakMode',
    params: { speakMode: 1 },
    id: 'mode-1'
});

// 更新页面状态
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'updateBackgroundInfo',
    params: { status: 'WaitCardPage' },
    id: 'updateBg-1'
});

// 添加消息到会话
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'addMessages',
    params: {
        messages: [
            { role: 'user', content: '用户消息' },
            { role: 'assistant', content: 'AI回复' }
        ]
    },
    id: 'addMsg-1'
});
```

## ⚙️ 配置说明

```javascript
const config = {
    hksttUrl: 'ws://your-server:8001',    // 语音识别服务
    aiServerUrl: 'http://your-server:8080', // AI服务
    ttsUrl: 'http://your-server:8080',    // 语音合成服务（可选）
    deviceId: 'your-device-id',           // 设备ID（可选，默认"1234567890"）
    organizationId: 206,                  // 组织ID（可选，默认206）
    debug: false                          // 生产环境设为false
};
```

## 🎉 完成

现在您已经成功集成WebSDK！

**下一步**：
- 查看 [使用指南](./WebSDK-使用指南.md) 了解更多功能
- 参考 [技术文档](./WebSDK-JSON-RPC-技术文档.md) 了解协议详情

---

**WebSDK Team** ❤️
