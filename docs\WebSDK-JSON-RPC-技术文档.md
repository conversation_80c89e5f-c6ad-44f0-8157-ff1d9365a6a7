# WebSDK JSON-RPC 2.0 技术文档

## 📋 概述

WebSDK基于JSON-RPC 2.0标准协议实现双向通信，支持AI对话、TTS语音合成、STT语音识别和数字人交互等功能。本文档详细描述了协议规范、消息格式、会话管理和错误处理机制。

## 🎯 核心特性

- **标准协议**：完全遵循JSON-RPC 2.0规范
- **双向通信**：支持客户端到服务端和服务端到客户端的消息传递
- **智能会话管理**：自动检测活跃组件并管理会话ID
- **多传输层支持**：WebSocket、HTTP/SSE等多种传输协议
- **流式响应**：支持实时流式数据传输
- **链式操作**：支持复杂的多步骤UI操作流程

## 🔄 会话ID自动管理机制

### 核心特性

WebSDK实现了智能的会话ID自动管理机制，大幅简化了使用方式：

1. **完全自动化**：用户无需手动传递sessionId参数，SDK自动检测当前活跃组件并使用正确的会话ID
2. **智能路由**：消息自动路由到当前活跃的UI组件（greeting-page或chat-widget）
3. **语言同步**：TTS播报语言与UI显示语言自动保持一致
4. **向后兼容**：即使用户传递了sessionId参数，SDK也会用内部管理的会话ID覆盖，确保系统稳定性

### 活跃组件检测

SDK自动检测以下信息：
- **当前活跃组件**：greeting-page（打招呼页面）或 chat-widget（聊天组件）
- **真实会话ID**：从MessageRouter获取当前组件的会话ID
- **语言设置**：从localStorage获取当前语言设置（mandarin/chuanyu）

### 使用建议

- **推荐做法**：在所有JSON-RPC请求中省略sessionId参数，让SDK自动管理
- **调试信息**：所有方法的返回值都包含实际使用的sessionId、目标组件、语言等调试信息
- **错误处理**：如果检测到无效的会话状态，SDK会自动创建新的会话

## 🌐 传输层架构

JSON-RPC协议与传输层无关，支持多种传输方式：

- **WebSocket**：实时双向通信，支持HKSTT语音识别服务
- **HTTP/SSE**：HTTP请求+服务端推送事件，支持TTS语音合成
- **内存消息**：SDK内部模块间通信
- **扩展协议**：未来可支持其他传输协议

权限验证、超时处理、重连机制等都在传输层完成，JSON-RPC层专注于消息格式和业务逻辑。

## 📝 消息协议格式

WebSDK严格遵循JSON-RPC 2.0规范，所有消息都采用标准的JSON-RPC格式。

### 基础请求格式

```json
{
  "jsonrpc": "2.0",        // 协议版本，固定为"2.0"
  "method": "speak",       // 方法名，使用驼峰命名
  "params": {              // 方法参数（可选）
    "text": "你好，欢迎使用WebSDK",
    "delay": 1000,
    "display": true
  },
  "id": "req_001"         // 请求ID，用于匹配响应
}
```

### 基础响应格式

```json
{
  "jsonrpc": "2.0",          // 协议版本，固定为"2.0"
  "result": {                // 成功响应结果
    "success": true,
    "message": "TTS播放已开始",
    "sessionId": "auto-managed-session-id",
    "targetComponent": "greeting-page",
    "language": "mandarin"
  },
  "id": "req_001"           // 响应ID，与请求ID匹配
}
```

### 错误响应格式

```json
{
  "jsonrpc": "2.0",
  "error": {                // 错误信息对象
    "code": -32603,         // 标准错误码
    "message": "内部错误",   // 错误描述
    "data": {               // 附加错误数据（可选）
      "details": "TTS服务连接失败",
      "timestamp": 1640995200000
    }
  },
  "id": "req_001"          // 错误响应也要匹配原始请求
}
```

### 通知格式（无响应）

```json
{
  "jsonrpc": "2.0",          // 协议版本
  "method": "notifications/userInput",  // 通知方法名
  "params": {                // 通知参数
    "userInput": "我想查询余额",
    "requestId": "user-input-001",
    "sessionId": "auto-managed-session"
  }
  // 注意：通知没有id字段，不需要响应
}
```

## ⚠️ 标准错误码

| 错误码 | 错误名称 | 描述 | 示例场景 |
|--------|----------|------|----------|
| -32700 | Parse error | JSON解析错误 | 消息格式错误、语法错误 |
| -32600 | Invalid Request | 无效请求 | 缺少必需字段、字段类型错误 |
| -32601 | Method not found | 方法未找到 | 调用不存在的方法 |
| -32602 | Invalid params | 无效参数 | 参数类型错误、缺少必需参数 |
| -32603 | Internal error | 内部错误 | 服务器内部处理错误 |

### 自定义错误码

| 错误码 | 错误名称 | 描述 | 示例场景 |
|--------|----------|------|----------|
| -32000 | Session error | 会话错误 | 会话ID无效或已过期 |
| -32001 | TTS error | TTS服务错误 | 语音合成失败 |
| -32002 | AI error | AI服务错误 | AI对话服务不可用 |
| -32003 | HKSTT error | 语音识别错误 | STT服务连接失败 |

## 🗂️ 请求管理机制

### Pending请求管理

客户端维护pending请求ID集合，实现请求生命周期管理：

```typescript
// 请求管理示例
class RequestManager {
  private pendingRequests = new Set<string>();
  
  async sendRequest(request: JsonRpcRequest): Promise<any> {
    // 添加到pending集合
    this.pendingRequests.add(request.id);
    
    try {
      const response = await this.transport.send(request);
      return response;
    } finally {
      // 请求完成后移除
      this.pendingRequests.remove(request.id);
    }
  }
  
  // 取消所有pending请求
  cancelAllRequests(): void {
    this.pendingRequests.clear();
  }
}
```

### 事件丢弃机制

- **自动清理**：当用户跳转新页面或取消操作时，清空pending集合
- **响应过滤**：只有在pending集合中的请求ID对应的响应才会被处理
- **优势**：相比pageId方式更精确，可以过滤同页面内的已取消操作

## 🔄 流式响应设计

### 请求格式

客户端通过标准JSON-RPC请求启用流式响应：

```json
{
  "jsonrpc": "2.0",
  "method": "api/ai/chat",
  "params": {
    "message": "你好，世界",
    "stream": true              // 可选：显式启用流式响应
  },
  "id": "req_003"
}
```

### 流式响应格式

服务端通过notification推送流式内容，使用请求ID进行匹配：

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/chatStreamResponse",
  "params": {
    "message": "你好，",
    "requestId": "req_003",        // 原始请求ID
    "isComplete": false           // 是否为最后一个片段
  }
}
```

### 最终响应

流式响应结束后，发送完整的最终响应：

```json
{
  "jsonrpc": "2.0",
  "id": "req_003",
  "result": {
    "message": "你好，我是AI助手，很高兴为您服务！",
    "sessionId": "auto-managed-session-id",
    "isComplete": true
  }
}
```

## 🔗 链式UI事件响应设计

当AI需要执行多个UI操作时，采用链式响应机制：

### 单步操作响应

```json
{
  "jsonrpc": "2.0",
  "id": "req_001",
  "result": {
    "message": "开始为您注册账号",
    "action": "register",
    "data": {
      "userType": "individual"
    }
    // 无nextRequestId，操作结束
  }
}
```

### 多步操作响应

第一步响应：
```json
{
  "jsonrpc": "2.0", 
  "id": "req_001",
  "result": {
    "message": "开始注册流程，正在跳转页面",
    "action": "register",
    "data": {
      "userType": "individual"
    }, 
    "nextRequestId": "req_002"  // 指示下一步的响应ID
  }
}
```

第二步响应：
```json
{
  "jsonrpc": "2.0",
  "id": "req_002", 
  "result": {
    "message": "正在填写用户信息",
    "action": "update",
    "type": "form",
    "data": {
      "name": "用户姓名", 
      "email": "<EMAIL>"
    },
    "nextRequestId": "req_003"
  }
}
```

### 客户端处理逻辑

1. 发送消息后，将请求ID加入pending列表
2. 收到响应后，从pending列表移除当前ID，执行UI操作
3. 如果响应包含nextRequestId，将其加入pending列表
4. 当需要取消操作时，清空pending列表，后续响应将被忽略

## 📡 服务端反向请求

服务端可以向客户端发送请求，获取用户输入或执行特定操作：

### 获取用户输入

```json
{
  "jsonrpc": "2.0",
  "method": "client/ui/getUserInput",
  "params": {
    "prompt": "请输入您的密码",
    "required": true,
    "timeout": 30000,
    "inputType": "password"
  },
  "id": "server_req_003"
}
```

### 客户端响应

```json
{
  "jsonrpc": "2.0",
  "result": {
    "value": "123456",
    "cancelled": false,
    "timestamp": 1640995200000
  },
  "id": "server_req_003"
}
```

## 🎤 ASR识别结果自动同步

### 功能概述

WebSDK实现了ASR识别结果自动同步到用户输入通知的功能。当收到 `notifications/asrOfflineResult` 通知时，系统会自动将识别到的文本内容同步发送到 `notifications/userInput` 方法。

### ASR识别结果通知

```json
{
  "jsonrpc": "2.0",        
  "method": "notifications/asrOfflineResult",
  "params": {
    "sid": "asr-session-uuid",    
    "text": "我想改密码"     
  }      
}
```

### 自动同步的用户输入通知

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/userInput",
  "params": {
    "userInput": "我想改密码",
    "requestId": "asr-sync-generated-uuid",
    "sessionId": "active-component-session-id"
  }
}
```

### 技术实现

1. **监听ASR结果**: `JsonRpcNotificationManager` 监听 `notifications/asrOfflineResult` 事件
2. **提取文本内容**: 从ASR识别结果中提取 `text` 字段
3. **生成请求参数**:
   - `requestId`: 使用UUID格式自动生成
   - `sessionId`: 使用当前活跃UI组件的会话ID（而非ASR会话ID）
   - `userInput`: ASR识别的文本内容
4. **同步调用**: 将参数传递给 `notifications/userInput` 处理逻辑
5. **事件发送**: 同时发送JSON-RPC通知给客户端监听器

### 会话ID管理

- **ASR会话ID**: ASR服务使用的会话标识符
- **UI组件会话ID**: WebSDK内部管理的对话会话标识符
- **自动同步**: 系统自动使用UI组件会话ID，确保与手动输入的会话一致性

## 📤 客户端可发送的事件

### 1. speak - 语音播报

让AI播报文字和音频。

```json
{
  "jsonrpc": "2.0",
  "method": "speak",
  "params": {
    "text": "你好，欢迎使用",          // 说话内容，编码UTF-8，必填
    "delay": 1000,                   // 非负整数，延时delay毫秒数后说话，可选，缺省值为0
    "display": true                  // 是否显示到气泡上，可选，缺省值为true
    // 注意：sessionId参数已由SDK自动管理，无需手动传递
  },
  "id": "req_001"
}
```

如果display为true，额外增加添加消息事件：

```json
{
  "jsonrpc": "2.0",
  "method": "addMessages",
  "params": {
    // sessionId由SDK自动管理，无需手动传递
    "messages": [        // 消息列表，必须符合OpenAI的消息格式
       {
          "role": "assistant",
          "content": "speak的内容"
       }
    ]
  },
  "context": {
      "deviceId": "your-device-id",     // 通过SDK初始化参数传入，默认"1234567890"
      "organizationId": 206,            // 通过SDK初始化参数传入，默认206
      "locale": "zh-CN"
  },
  "id": "req_002"
}
```

**响应格式**：
```json
{
  "jsonrpc": "2.0",
  "result": {
    "sessionId": "auto-managed-session-id"  // SDK自动管理的会话ID
    // 注意：实际响应格式由AI服务器决定，SDK确保使用正确的会话ID
  },
  "id": "req_002"
}
```

### 2. updateBackgroundInfo - 背景信息更新

更新页面变化、状态变化等背景信息。

#### 🆕 页面状态切换功能
支持通过`status`参数实现页面状态切换，支持三种页面状态：
- **WaitCardPage**：全功能页面（显示常用功能区域和所有业务卡片）
- **AppSelectAuto**：储蓄卡业务页面（隐藏常用功能区域，显示储蓄相关业务卡片）
- **CreditSelectAuto**：信用卡业务页面（隐藏常用功能区域，显示信用卡相关业务卡片）

```json
{
  "jsonrpc": "2.0",
  "method": "updateBackgroundInfo",
  "params": {
    // sessionId由SDK自动管理，无需手动传递
    "page": "1.2-Greeting-Dialogue-Page",   // 当前的页面Id（可选）
    "status": "CreditSelectAuto"              // 页面状态切换：WaitCardPage|AppSelectAuto|CreditSelectAuto
  },
  "context": {
      "deviceId": "your-device-id",     // 通过SDK初始化参数传入，默认"1234567890"
      "organizationId": 206,            // 通过SDK初始化参数传入，默认206
      "locale": "zh-CN"
  },
  "id": "req_003"
}
```

**响应格式**：
```json
{
  "jsonrpc": "2.0",
  "result": {
    "sessionId": "abcd"        // 更新背景信息所在的会话id
  },
  "id": "req_003"
}
```

### 3. addMessages - 添加消息

添加消息列表到会话历史中，但不触发AI回复。

```json
{
  "jsonrpc": "2.0",
  "method": "addMessages",
  "params": {
    "sessionId": "abcd",     // 会话id，如果传的有值，则找到对应会话更新数据，如果没传值，会在响应消息里带上sessionId
    "messages": [        // 消息列表，必须符合OpenAI的消息格式
       {
          "role": "user 或 assistant",
          "content": "消息内容"
       }
    ]
  },
  "context": {
      "deviceId": "your-device-id",     // 通过SDK初始化参数传入，默认"1234567890"
      "organizationId": 206,            // 通过SDK初始化参数传入，默认206
      "locale": "zh-CN"
  },
  "id": "req_004"
}
```

**响应格式**：
```json
{
  "jsonrpc": "2.0",
  "result": {
    "sessionId": "abcd"        // 更新背景信息所在的会话id
  },
  "id": "req_004"
}
```

### 4. pushBizData - 推送业务数据

推送一些动态数据，比如今天的新闻、在班的工作人员等信息。

```json
{
  "jsonrpc": "2.0",
  "method": "pushBizData",        // 推送业务数据
  "params": {
    "key": "news",            // 业务数据的key值
    "data": {                // 业务数据
      // 具体业务数据内容
    }
  },
  "context": {
      "deviceId": "your-device-id",     // 通过SDK初始化参数传入，默认"1234567890"
      "organizationId": 206,            // 通过SDK初始化参数传入，默认206
      "locale": "zh-CN"
  },
  "id": "req_005"
}
```

**响应格式**：
```json
{
  "jsonrpc": "2.0",
  "result": {
    "success": true      // 成功只有这个结果，失败会返回error
  },
  "id": "req_005"
}
```

### 5. speakMode - 设置收音模式

设置语音识别的收音模式。**注意：切换收音模式时会自动先调用stopSession停止当前收音会话。**

```json
{
  "jsonrpc": "2.0",
  "method": "speakMode",
  "params": {
    "speakMode": 1    // 收音模式：0=唇动收音，1=点击收音
  },
  "id": "req_006"
}
```

**参数说明**：
- `speakMode`: 收音模式（0=唇动收音，1=点击收音）

### 6. stopSession - 停止收音会话

停止当前收音会话，丢弃当前的收音内容。通常在切换收音模式或页面状态时自动调用。

```json
{
  "jsonrpc": "2.0",
  "method": "stopSession",
  "params": {},
  "id": "req_007"
}
```

**参数说明**：
- 无参数

## 📥 服务端通知事件

### 1. notifications/userInput - 用户输入通知

当用户通过语音或文字输入时触发：

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/userInput",
  "params": {
    "userInput": "我想查询余额",
    "requestId": "user-input-001",
    "sessionId": "auto-managed-session"
  }
}
```

### 2. notifications/aiResponse - AI响应通知

AI服务返回响应时触发：

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/aiResponse",
  "params": {
    "message": "您的余额是1000元",
    "action": "query_balance",
    "data": { "balance": 1000 },
    "requestId": "ai-response-001"
  }
}
```

### 3. notifications/businessButtonClick - 业务按钮点击

用户点击业务按钮时触发：

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/businessButtonClick",
    "buttonId": "transfer",
    "buttonText": "转账",
    "requestId": "button-click-001"
  }
}
```

## 🔔 通知事件列表

### 1. notifications/modelStatus - 模型状态

**描述**: FunASR模型初始化状态通知

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/modelStatus",
  "params": {
    "loaded": true
  }
}
```

### 2. notifications/faceStatus - 人脸检测

**描述**: 人脸检测状态通知

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/faceStatus",
  "params": {
    "hasFace": true
  }
}
```

### 3. notifications/asrOfflineResult - ASR识别结果

**描述**: ASR离线识别结果通知

```json
{
  "jsonrpc": "2.0",        
  "method": "notifications/asrOfflineResult",
  "params": {
    "sid": "asr-session-uuid",    
    "text": "我想改密码"     
  }      
}
```

### 4. notifications/asrSessionComplete - ASR会话结束

**描述**: ASR单轮会话结束通知

```json
{
  "jsonrpc": "2.0",        
  "method": "notifications/asrSessionComplete",
  "params": {}      
}
```

### 5. notifications/newUser - 新用户检测

**描述**: 摄像头识别到新用户进入

```json
{
  "jsonrpc": "2.0",        
  "method": "notifications/newUser",
  "params": {}      
}
```

### 6. notifications/userInput - 用户输入

**描述**: 用户输入事件（包括ASR自动同步和手动输入）

```json
{
  "jsonrpc": "2.0",        
  "method": "notifications/userInput",
  "params": {
    "userInput": "我想改密码",
    "requestId": "user-input-uuid",
    "sessionId": "active-component-session-id"
  }      
}
```

### 7. notifications/aiResponse - AI响应

**描述**: AI服务响应通知，包含业务操作和数据更新

```json
{
  "jsonrpc": "2.0",        
  "method": "notifications/aiResponse",   
  "params": {
    "requestId": "req_003",
    "message": "请先登录您的账号",
    "action": "login",
    "data": {
      "type": "idCard"
    }
  }      
}
```

### 8. notifications/chatStreamResponse - 流式聊天响应

**描述**: AI聊天的流式响应，用于实时显示AI回复

```json
{
  "jsonrpc": "2.0",
  "method": "notifications/chatStreamResponse",
  "params": {
    "message": "好的",
    "requestId": "chat-uuid",
    "sessionId": "active-session-id"
  }
}
```

### 9. notifications/status - 状态信息

**描述**: 中途的状态信息，用于显示处理进度

```json
{
  "jsonrpc": "2.0",        
  "method": "notifications/status",
  "params": {
    "requestId": "req_003",
    "message": "正在分析数据..."
  }      
}
```

## 🛠️ 最佳实践

### 1. 请求ID命名规范

```typescript
// 推荐的ID命名模式
const requestId = `${method}-${timestamp}-${sequence}`;

// 示例
const speakId = `speak-${Date.now()}-001`;
const chatId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
```

### 2. 错误重试机制

```typescript
async function sendWithRetry(message: JsonRpcRequest, maxRetries = 3): Promise<any> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const retryMessage = {
        ...message,
        id: `${message.id}-retry-${i}`
      };
      
      return await sdk.sendJsonRpcMessage(retryMessage);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 3. 请求去重

```typescript
const sentRequests = new Set<string>();

function sendUniqueRequest(message: JsonRpcRequest): Promise<any> {
  if (sentRequests.has(message.id)) {
    throw new Error(`请求ID ${message.id} 已发送过`);
  }
  
  sentRequests.add(message.id);
  return sdk.sendJsonRpcMessage(message);
}
```

### 4. 会话状态监控

```typescript
// 监控SDK的会话管理状态
const responses = [];

for (const text of ['消息1', '消息2', '消息3']) {
  const response = await sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: { text },
    id: `batch-speak-${Date.now()}`
  });

  responses.push({
    text,
    sessionId: response.sessionId,
    targetComponent: response.targetComponent,
    language: response.language
  });
}

// 验证所有消息是否使用了一致的会话ID
const uniqueSessionIds = new Set(responses.map(r => r.sessionId));
console.log(`使用了 ${uniqueSessionIds.size} 个不同的会话ID`);
```

## 🔍 调试和监控

### 启用调试模式

```typescript
const sdk = await WebSDK.init({
  hksttUrl: 'ws://localhost:8001',
  aiServerUrl: 'http://localhost:8080',
  debug: true  // 启用详细日志
});
```

### 监听所有事件

```typescript
// 监听所有JSON-RPC事件
sdk.eventBus.on('*', (eventName, data) => {
  console.log(`事件: ${eventName}`, data);
});
```

### 检查连接状态

```typescript
// 检查SDK状态
const status = sdk.getStatus();
console.log('SDK状态:', {
  isInitialized: status.isInitialized,
  isConnected: status.isConnected,
  isReady: status.isReady
});
```

---

**WebSDK Team** ❤️ 专业打造
