# WebSDK 文档中心

## 📚 文档概览

欢迎使用WebSDK！这里是完整的文档中心，包含了从快速开始到深入使用的所有指南。

## 🚀 快速开始

如果您是第一次使用WebSDK，建议按以下顺序阅读文档：

1. **[快速开始指南](./WebSDK-快速开始指南.md)** - 5分钟快速上手
2. **[使用指南](./WebSDK-使用指南.md)** - 详细的功能介绍和示例
3. **[JSON-RPC技术文档](./WebSDK-JSON-RPC-技术文档.md)** - 协议规范和技术细节

## 📖 核心文档

### 🎯 用户指南

| 文档 | 描述 | 适合人群 |
|------|------|----------|
| [快速开始指南](./WebSDK-快速开始指南.md) | 5分钟快速上手，包含完整示例 | 所有开发者 |
| [使用指南](./WebSDK-使用指南.md) | 详细的安装、配置、API参考和代码示例 | 应用开发者 |

### 🔧 技术文档

| 文档 | 描述 | 适合人群 |
|------|------|----------|
| [JSON-RPC技术文档](./WebSDK-JSON-RPC-技术文档.md) | 协议规范、消息格式、会话管理 | 系统集成者 |
| [业务按钮点击事件使用指南](./业务按钮点击事件使用指南.md) | 业务按钮事件监听和处理 | 业务开发者 |

### 🎤 服务集成文档

| 文档 | 描述 | 适合人群 |
|------|------|----------|
| [TTS API使用文档](./TTS_API_使用文档.md) | CosyVoice TTS服务详细使用指南 | 语音开发者 |
| [CosyVoice WebSocket API文档](./CosyVoice_WebSocket_API_文档.md) | WebSocket版本的TTS API | 实时语音开发者 |

## 🎯 按使用场景选择文档

### 🆕 我是新手，想快速了解WebSDK

**推荐路径**：
1. 阅读 [快速开始指南](./WebSDK-快速开始指南.md)
2. 运行示例代码
3. 查看 [使用指南](./WebSDK-使用指南.md) 中的基础示例

### 🔨 我要开发聊天应用

**推荐路径**：
1. [快速开始指南](./WebSDK-快速开始指南.md) - 了解基础
2. [使用指南](./WebSDK-使用指南.md) - 查看聊天应用示例
3. [JSON-RPC技术文档](./WebSDK-JSON-RPC-技术文档.md) - 了解消息格式
4. [最佳实践](./WebSDK-最佳实践.md) - 错误处理和性能优化

### 🎤 我要集成语音功能

**推荐路径**：
1. [使用指南](./WebSDK-使用指南.md) - 语音功能介绍
2. [TTS API使用文档](./TTS_API_使用文档.md) - TTS服务详解
3. [CosyVoice WebSocket API文档](./CosyVoice_WebSocket_API_文档.md) - WebSocket TTS
4. [最佳实践](./WebSDK-最佳实践.md) - 语音功能优化

### 🏢 我要开发银行业务应用

**推荐路径**：
1. [使用指南](./WebSDK-使用指南.md) - 基础功能
2. [业务按钮点击事件使用指南](./业务按钮点击事件使用指南.md) - 业务事件处理
3. [JSON-RPC技术文档](./WebSDK-JSON-RPC-技术文档.md) - 业务消息格式
4. [最佳实践](./WebSDK-最佳实践.md) - 业务流程优化

### ⚛️ 我使用React框架

**推荐路径**：
1. [快速开始指南](./WebSDK-快速开始指南.md) - 基础了解
2. [使用指南](./WebSDK-使用指南.md) - React组件集成部分
3. [最佳实践](./WebSDK-最佳实践.md) - React性能优化

### 🔧 我要进行系统集成

**推荐路径**：
1. [JSON-RPC技术文档](./WebSDK-JSON-RPC-技术文档.md) - 协议详解
2. [使用指南](./WebSDK-使用指南.md) - API参考
3. [最佳实践](./WebSDK-最佳实践.md) - 安全和监控
4. 相关服务API文档

## 🔍 快速查找

### 常用API

- **初始化SDK**: [使用指南 - 安装和配置](./WebSDK-使用指南.md#安装和配置)
- **发送消息**: [JSON-RPC技术文档 - 支持的方法](./WebSDK-JSON-RPC-技术文档.md#支持的方法列表)
- **事件监听**: [使用指南 - EventBus事件系统](./WebSDK-使用指南.md#eventbus事件系统)
- **错误处理**: [最佳实践 - 错误处理](./WebSDK-最佳实践.md#错误处理最佳实践)

### 常见问题

- **连接失败**: [最佳实践 - FAQ Q1](./WebSDK-最佳实践.md#q1-sdk初始化失败怎么办)
- **语音无声音**: [最佳实践 - FAQ Q2](./WebSDK-最佳实践.md#q2-语音播报没有声音)
- **语音识别不工作**: [最佳实践 - FAQ Q3](./WebSDK-最佳实践.md#q3-语音识别不工作)
- **会话管理**: [最佳实践 - FAQ Q4](./WebSDK-最佳实践.md#q4-如何处理会话管理)

### 代码示例

- **基础聊天应用**: [使用指南 - 基础聊天应用](./WebSDK-使用指南.md#1-基础聊天应用)
- **语音交互应用**: [使用指南 - 语音交互应用](./WebSDK-使用指南.md#2-语音交互应用)
- **React组件**: [使用指南 - React组件集成](./WebSDK-使用指南.md#react组件集成)
- **完整示例**: [快速开始指南 - 完整示例](./WebSDK-快速开始指南.md)

## 📋 文档版本信息

| 文档 | 版本 | 更新日期 | 状态 |
|------|------|----------|------|
| 快速开始指南 | v1.0 | 2025-01-04 | ✅ 最新 |
| 使用指南 | v1.0 | 2025-01-04 | ✅ 最新 |
| JSON-RPC技术文档 | v1.0 | 2025-01-04 | ✅ 最新 |
| 最佳实践 | v1.0 | 2025-01-04 | ✅ 最新 |
| 业务按钮事件指南 | v1.0 | 2024-12-XX | ✅ 有效 |
| TTS API文档 | v1.0 | 2024-12-XX | ✅ 有效 |
| CosyVoice WebSocket API | v1.0 | 2024-12-XX | ✅ 有效 |

## 🆘 获取帮助

### 文档问题

如果您在使用文档过程中遇到问题：

1. **检查版本**: 确保使用的是最新版本的文档
2. **搜索FAQ**: 查看 [最佳实践文档](./WebSDK-最佳实践.md) 中的常见问题
3. **查看示例**: 参考 [快速开始指南](./WebSDK-快速开始指南.md) 中的完整示例
4. **调试模式**: 启用SDK的debug模式获取详细日志

### 技术支持

- **GitHub Issues**: 提交技术问题和功能请求
- **技术文档**: 查看详细的API文档和协议规范
- **示例代码**: 参考文档中的完整示例

## 🔄 文档更新日志

### v1.0 (2025-01-04)

**新增**:
- ✅ 全新的WebSDK文档体系
- ✅ 快速开始指南 - 5分钟上手指南
- ✅ 详细的使用指南 - 完整的API参考和示例
- ✅ JSON-RPC技术文档 - 协议规范和消息格式
- ✅ 最佳实践指南 - 错误处理、性能优化和FAQ

**优化**:
- 🔄 重新组织文档结构，提高可读性
- 🔄 统一文档格式和风格
- 🔄 添加详细的代码示例和使用场景

**移除**:
- ❌ 过时的JSON-RPC使用指南
- ❌ 产品需求文档(PRD)
- ❌ 测试套件重构总结
- ❌ 原型图相关文档

## 📝 文档贡献

我们欢迎社区贡献来改进文档质量：

1. **报告问题**: 发现文档错误或不清楚的地方
2. **提出建议**: 建议新的示例或使用场景
3. **完善内容**: 补充缺失的信息或改进现有内容

---

**WebSDK Team** ❤️ 让AI对话更简单

> 💡 **提示**: 建议将此文档加入书签，方便随时查阅。如果您是第一次使用，强烈推荐从 [快速开始指南](./WebSDK-快速开始指南.md) 开始！
