# WebSDK 使用指南

## 🎯 概述

WebSDK是一个JavaScript SDK，集成了AI对话、语音识别、语音合成等功能。

### 核心特性

- **🤖 AI对话**: 智能对话和响应
- **🎤 语音识别**: HKSTT语音识别
- **🔊 语音合成**: TTS语音播报
- **👤 数字人交互**: 数字人状态管理
- **🔄 自动会话管理**: 智能会话管理

## 🚀 安装配置

### 1. 引入SDK

```html
<script src="/static/websdk/websdk.min.js"></script>
```

### 2. 初始化

```javascript
WebSDK.init({
    hksttUrl: 'ws://your-server:8001',
    aiServerUrl: 'http://your-server:8080',
    deviceId: 'your-device-id',           // 可选，默认"1234567890"
    organizationId: 206,                  // 可选，默认206
    debug: false
}).then(sdk => {
    console.log('SDK初始化成功');
    window.sdk = sdk;
}).catch(error => {
    console.error('初始化失败:', error);
});
```

### 3. 配置参数

```javascript
const config = {
    hksttUrl: 'ws://server:8001',     // 语音识别服务
    aiServerUrl: 'http://server:8080', // AI服务
    ttsUrl: 'http://server:8080',     // 语音合成（可选）
    deviceId: 'your-device-id',       // 设备ID（可选，默认"1234567890"）
    organizationId: 206,              // 组织ID（可选，默认206）
    debug: false                      // 调试模式
};
```

## 🔧 核心功能

### 1. 事件监听

```javascript
// 监听用户输入
sdk.onNotification('notifications/userInput', (params) => {
    console.log('用户输入:', params.userInput);
});

// 监听AI响应
sdk.onNotification('notifications/aiResponse', (params) => {
    console.log('AI响应:', params.message);
});

// 监听业务按钮点击
sdk.onBusinessButtonClick((request) => {
    console.log('业务按钮:', request);
});
```

### 2. AI语音播报

```javascript
// AI语音播报
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: {
        text: '您好，欢迎使用银行服务',
        delay: 1000,    // 延时1秒播报（可选）
        display: true   // 显示在聊天气泡（可选，默认true）
    },
    id: 'speak-1'
});
```

### 3. 页面状态切换

```javascript
// 切换到储蓄卡业务页面
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'updateBackgroundInfo',
    params: {
        page: '1.2-Greeting-Dialogue-Page',
        status: 'AppSelectAuto'
    },
    id: 'updateBg-1'
});

// 切换到信用卡业务页面
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'updateBackgroundInfo',
    params: {
        status: 'CreditSelectAuto'
    },
    id: 'updateBg-2'
});
```

### 4. 添加消息到会话

```javascript
// 添加用户消息到会话历史
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'addMessages',
    params: {
        messages: [
            { role: 'user', content: '我想办理储蓄卡' },
            { role: 'assistant', content: '好的，我来为您介绍储蓄卡业务' }
        ]
    },
    id: 'addMsg-1'
});
```

### 5. 推送业务数据

```javascript
// 推送今日新闻
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'pushBizData',
    params: {
        key: 'news',
        data: {
            title: '银行最新公告',
            content: '新增手机银行功能...'
        }
    },
    id: 'pushBiz-1'
});

// 推送工作人员信息
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'pushBizData',
    params: {
        key: 'staff',
        data: {
            onDuty: ['张经理', '李顾问'],
            department: '个人业务部'
        }
    },
    id: 'pushBiz-2'
});
```

### 6. 收音模式

```javascript
// 设置收音模式：0=唇动收音，1=点击收音
// 注意：切换收音模式时会自动停止当前收音会话
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speakMode',
    params: { speakMode: 1 },
    id: 'mode-1'
});

// 手动停止当前收音会话（丢弃当前的收音）
sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'stopSession',
    params: {},
    id: 'stop-1'
});
```
### 7. 显示组件

```javascript
// 显示欢迎页面
sdk.showGreetingPage();

// 手动控制聊天组件显示
const chatWidget = document.querySelector('chat-widget');
if (chatWidget) {
    chatWidget.style.display = 'block';
}
```

### 8. 聊天对话

```javascript
// 发送用户消息到AI（使用AIClient）
sdk.getAIClient().chat({
    userInput: '你好，我想了解银行业务'
});
```

### 9. 语音识别

```javascript
// 开始语音识别
sdk.getHKSTTClient().startListening();

// 停止语音识别
sdk.getHKSTTClient().stopListening();
```

## 📚 API方法总览

### 初始化
```javascript
WebSDK.init(config)  // 初始化SDK
```

### 核心功能
```javascript
sdk.sendJsonRpcMessage(message)      // 发送JSON-RPC消息（核心方法）
sdk.getAIClient().chat(request)      // 发送聊天消息
```

### 组件控制
```javascript
sdk.showGreetingPage()               // 显示欢迎页面
// 其他组件需要手动控制DOM元素
```

### 语音功能
```javascript
sdk.getHKSTTClient().startListening() // 开始语音识别
sdk.getHKSTTClient().stopListening()  // 停止语音识别
```

### 事件监听
```javascript
sdk.onNotification(event, callback)     // 监听通知事件
sdk.onBusinessButtonClick(callback)     // 监听业务按钮
```

### 获取内部组件
```javascript
sdk.getAIClient()                    // 获取AI客户端
sdk.getHKSTTClient()                 // 获取语音识别客户端
sdk.getEventBus()                    // 获取事件总线
sdk.getConfig()                      // 获取SDK配置
```
## 💡 示例代码

### 完整示例

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>WebSDK示例</title>
</head>
<body>
    <chat-widget style="display: none;"></chat-widget>

    <script src="/static/websdk/websdk.min.js"></script>
    <script>
        WebSDK.init({
            hksttUrl: 'ws://server:8001',
            aiServerUrl: 'http://server:8080',
            deviceId: 'your-device-id',       // 可选，默认"1234567890"
            organizationId: 206,              // 可选，默认206
            debug: false
        }).then(sdk => {
            window.sdk = sdk;

            // 监听事件
            sdk.onNotification('notifications/userInput', (params) => {
                console.log('用户输入:', params.userInput);
            });

            sdk.onNotification('notifications/aiResponse', (params) => {
                console.log('AI响应:', params.message);
            });

            sdk.onBusinessButtonClick((request) => {
                console.log('业务按钮:', request);
            });

        }).catch(error => {
            console.error('初始化失败:', error);
        });
    </script>
</body>
</html>
```

---

**WebSDK Team** ❤️
